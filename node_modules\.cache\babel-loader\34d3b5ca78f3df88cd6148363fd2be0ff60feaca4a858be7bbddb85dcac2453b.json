{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\ZodiacPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport SmokeAnimation from './SmokeAnimation';\nimport KuberaAnimation from './KuberaAnimation';\nimport HoroscopeService from '../services/HoroscopeService';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\n\n// Advanced horoscope parser that extracts structured content\nconst parseHoroscopeIntoStructuredCategories = rawText => {\n  // Check if rawText is valid\n  if (!rawText || typeof rawText !== 'string') {\n    return [];\n  }\n\n  // Clean the raw text first\n  const cleanText = rawText.replace(/\\*\\*/g, '').replace(/##/g, '').replace(/\\*/g, '').replace(/\\[.*?\\]/g, '').trim();\n  const categories = {\n    love: {\n      id: 'love',\n      title: 'ආදරය සහ සම්බන්ධතා',\n      emoji: '💕',\n      icon: '❤️',\n      content: '',\n      keywords: ['ආදර', 'සම්බන්ධතා', 'ප්‍රේම', 'විවාහ', 'මිත්‍ර']\n    },\n    career: {\n      id: 'career',\n      title: 'වෘත්තීය ජීවිතය',\n      emoji: '💼',\n      icon: '🏢',\n      content: '',\n      keywords: ['වෘත්ති', 'කාර්', 'රැකියා', 'ව්‍යාපාර', 'සේවා']\n    },\n    health: {\n      id: 'health',\n      title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n      emoji: '🌿',\n      icon: '🏥',\n      content: '',\n      keywords: ['සෞඛ්', 'සෞඛ', 'යහපැවැත්ම', 'ශරීර', 'මානසික']\n    },\n    finance: {\n      id: 'finance',\n      title: 'මූල්‍ය කටයුතු',\n      emoji: '💰',\n      icon: '💳',\n      content: '',\n      keywords: ['මූල්', 'මුදල්', 'ආර්ථික', 'ආදායම', 'වියදම']\n    },\n    general: {\n      id: 'general',\n      title: 'සාමාන්‍ය උපදෙස්',\n      emoji: '✨',\n      icon: '🔮',\n      content: '',\n      keywords: ['සාමාන්', 'උපදෙස්', 'සාර්ථක', 'ජීවිත', 'දිනය']\n    }\n  };\n\n  // Split text into lines and process\n  const lines = cleanText.split('\\n').filter(line => line.trim().length > 0);\n  let currentCategory = null;\n  let contentBuffer = [];\n\n  // If no clear structure, distribute content evenly across categories\n  if (lines.length < 5) {\n    // Short content - put everything in general\n    categories.general.content = cleanText;\n  } else {\n    // Process each line to categorize content\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i].trim();\n      if (!line || line.length < 2) continue;\n\n      // Detect category by keywords or numbered sections\n      let detectedCategory = null;\n\n      // Check for numbered sections (1., 2., 3., etc.)\n      const numberedMatch = line.match(/^(\\d+)\\./);\n      if (numberedMatch) {\n        const num = parseInt(numberedMatch[1]);\n        const categoryOrder = ['love', 'career', 'health', 'finance', 'general'];\n        if (num >= 1 && num <= 5) {\n          detectedCategory = categoryOrder[num - 1];\n        }\n      }\n\n      // Check for keyword-based detection (more flexible)\n      if (!detectedCategory) {\n        for (const [catId, catData] of Object.entries(categories)) {\n          for (const keyword of catData.keywords) {\n            if (line.toLowerCase().includes(keyword.toLowerCase())) {\n              detectedCategory = catId;\n              break;\n            }\n          }\n          if (detectedCategory) break;\n        }\n      }\n\n      // If we found a new category, save previous content\n      if (detectedCategory && detectedCategory !== currentCategory) {\n        if (currentCategory && contentBuffer.length > 0) {\n          categories[currentCategory].content = contentBuffer.join(' ').trim();\n        }\n        currentCategory = detectedCategory;\n        contentBuffer = [];\n\n        // Clean the line and add to buffer\n        let cleanContent = line.replace(/^\\d+\\.\\s*/, '').replace(/^[•-]\\s*/, '').replace(new RegExp(categories[detectedCategory].title, 'gi'), '').replace(/:/g, '').trim();\n        if (cleanContent.length > 0) {\n          contentBuffer.push(cleanContent);\n        }\n      } else if (currentCategory) {\n        // Add content to current category\n        let cleanContent = line.trim();\n        if (cleanContent.length > 0) {\n          contentBuffer.push(cleanContent);\n        }\n      } else {\n        // No category detected yet, start with general and add content\n        currentCategory = 'general';\n        contentBuffer.push(line.trim());\n      }\n    }\n\n    // If no categories were detected, distribute content intelligently\n    if (!Object.values(categories).some(cat => cat.content)) {\n      const sentences = cleanText.split(/[.!?]/).filter(s => s.trim().length > 10);\n      const categoriesArray = Object.keys(categories);\n      sentences.forEach((sentence, index) => {\n        const categoryIndex = index % categoriesArray.length;\n        const categoryKey = categoriesArray[categoryIndex];\n        if (!categories[categoryKey].content) {\n          categories[categoryKey].content = sentence.trim();\n        } else {\n          categories[categoryKey].content += ' ' + sentence.trim();\n        }\n      });\n    }\n  }\n\n  // Save final category content\n  if (currentCategory && contentBuffer.length > 0) {\n    categories[currentCategory].content = contentBuffer.join(' ').trim();\n  }\n\n  // Ensure all categories have meaningful content\n  Object.values(categories).forEach((category, index) => {\n    if (!category.content || category.content.length < 5) {\n      // If still no content, use a portion of the original text\n      const sentences = cleanText.split(/[.!?]/).filter(s => s.trim().length > 5);\n      if (sentences.length > index) {\n        category.content = sentences[index].trim() || cleanText.substring(index * 50, (index + 1) * 50).trim();\n      } else {\n        // Last resort - use generic content based on category\n        const genericContent = {\n          love: 'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n          career: 'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.',\n          health: 'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.',\n          finance: 'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.',\n          general: 'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'\n        };\n        category.content = genericContent[category.id] || 'ධනාත්මක වෙනස්කම් සහ සාර්ථකත්වය අපේක්ෂා කරන්න.';\n      }\n    }\n  });\n\n  // Ensure each category has its id properly set and return as array\n  return Object.entries(categories).map(([key, category]) => ({\n    ...category,\n    id: key // Ensure id is properly set\n  }));\n};\n\n// Beautiful category card component\nconst CategoryCard = ({\n  category,\n  index\n}) => {\n  const cardStyles = {\n    love: {\n      background: 'linear-gradient(135deg, rgba(255, 182, 193, 0.1) 0%, rgba(255, 105, 180, 0.05) 100%)',\n      border: '1px solid rgba(255, 182, 193, 0.3)',\n      shadow: '0 8px 32px rgba(255, 105, 180, 0.1)'\n    },\n    career: {\n      background: 'linear-gradient(135deg, rgba(70, 130, 180, 0.1) 0%, rgba(30, 144, 255, 0.05) 100%)',\n      border: '1px solid rgba(70, 130, 180, 0.3)',\n      shadow: '0 8px 32px rgba(30, 144, 255, 0.1)'\n    },\n    health: {\n      background: 'linear-gradient(135deg, rgba(144, 238, 144, 0.1) 0%, rgba(50, 205, 50, 0.05) 100%)',\n      border: '1px solid rgba(144, 238, 144, 0.3)',\n      shadow: '0 8px 32px rgba(50, 205, 50, 0.1)'\n    },\n    finance: {\n      background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%)',\n      border: '1px solid rgba(255, 215, 0, 0.3)',\n      shadow: '0 8px 32px rgba(255, 165, 0, 0.1)'\n    },\n    general: {\n      background: 'linear-gradient(135deg, rgba(221, 160, 221, 0.1) 0%, rgba(147, 112, 219, 0.05) 100%)',\n      border: '1px solid rgba(221, 160, 221, 0.3)',\n      shadow: '0 8px 32px rgba(147, 112, 219, 0.1)'\n    }\n  };\n  const style = cardStyles[category.id] || cardStyles.general;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"horoscope-category-card\",\n    style: {\n      marginBottom: '2rem',\n      padding: '2rem',\n      background: style.background,\n      border: style.border,\n      borderRadius: '20px',\n      boxShadow: style.shadow,\n      backdropFilter: 'blur(10px)',\n      transition: 'all 0.3s ease',\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    onMouseEnter: e => {\n      e.currentTarget.style.transform = 'translateY(-5px)';\n      e.currentTarget.style.boxShadow = style.shadow.replace('0.1)', '0.2)');\n    },\n    onMouseLeave: e => {\n      e.currentTarget.style.transform = 'translateY(0)';\n      e.currentTarget.style.boxShadow = style.shadow;\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '-50%',\n        right: '-50%',\n        width: '200%',\n        height: '200%',\n        background: `radial-gradient(circle, ${style.border.replace('1px solid ', '').replace('0.3)', '0.05)')} 1px, transparent 1px)`,\n        backgroundSize: '20px 20px',\n        opacity: 0.3,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 8\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        marginBottom: '1.5rem',\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '2.5rem',\n          marginRight: '1rem',\n          filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n        },\n        children: category.emoji\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 10\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#f4d03f',\n            fontSize: '1.4rem',\n            margin: 0,\n            fontFamily: 'Noto Sans Sinhala, sans-serif',\n            fontWeight: '600',\n            textShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          },\n          children: category.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '50px',\n            height: '3px',\n            background: 'linear-gradient(90deg, #f4d03f, transparent)',\n            marginTop: '0.5rem',\n            borderRadius: '2px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 12\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 10\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 8\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#e8f4fd',\n          lineHeight: '1.8',\n          fontSize: '1.1rem',\n          margin: 0,\n          fontFamily: 'Noto Sans Sinhala, sans-serif',\n          textAlign: 'justify',\n          textShadow: '0 1px 2px rgba(0,0,0,0.1)'\n        },\n        children: category.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 10\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 8\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        bottom: 0,\n        left: 0,\n        right: 0,\n        height: '4px',\n        background: `linear-gradient(90deg, ${style.border.replace('1px solid ', '').replace('0.3)', '0.6)')}, transparent)`,\n        borderRadius: '0 0 20px 20px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 8\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 241,\n    columnNumber: 6\n  }, this);\n};\n\n// Main display component\n_c = CategoryCard;\nconst StructuredHoroscopeDisplay = ({\n  horoscope,\n  uiTexts\n}) => {\n  let categories;\n\n  // Check if we have structured data from the new API\n  if (horoscope && horoscope.structured && horoscope.categories) {\n    // Handle both object and array formats\n    if (Array.isArray(horoscope.categories)) {\n      categories = horoscope.categories;\n    } else {\n      // Convert object to array with proper ids\n      categories = Object.entries(horoscope.categories).map(([key, category]) => ({\n        ...category,\n        id: category.id || key\n      }));\n    }\n  } else if (typeof horoscope === 'string') {\n    // Fallback to parsing raw text\n    categories = parseHoroscopeIntoStructuredCategories(horoscope);\n  } else {\n    categories = [];\n  }\n\n  // Fallback if no categories found\n  if (!categories || categories.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '2rem',\n        color: '#e8f4fd',\n        fontFamily: 'Noto Sans Sinhala, sans-serif'\n      },\n      children: (uiTexts === null || uiTexts === void 0 ? void 0 : uiTexts.generatingHoroscope) || 'රාශිඵල සකස් කරමින්... කරුණාකර රැඳී සිටින්න.'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"structured-horoscope-display\",\n    style: {\n      maxWidth: '800px',\n      margin: '0 auto',\n      padding: '1rem'\n    },\n    children: categories.map((category, index) => /*#__PURE__*/_jsxDEV(CategoryCard, {\n      category: category,\n      index: index\n    }, category.id || `category-${index}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 399,\n    columnNumber: 5\n  }, this);\n};\n_c2 = StructuredHoroscopeDisplay;\nconst ZodiacPage = ({\n  sign\n}) => {\n  _s();\n  const [horoscope, setHoroscope] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [soundEnabled, setSoundEnabled] = useState(true); // Default to true (on)\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [translatedHoroscope, setTranslatedHoroscope] = useState(null);\n\n  // Language context\n  const {\n    currentLanguage,\n    translateHoroscopeCategories,\n    isTranslating,\n    getUITexts\n  } = useLanguage();\n  const uiTexts = getUITexts();\n\n  // Analytics integration\n  const analytics = useAnalytics();\n  useComponentTracking('ZodiacPage');\n  const [refreshing, setRefreshing] = useState(false);\n  const [userInteracted, setUserInteracted] = useState(false);\n  const [showAudioPrompt, setShowAudioPrompt] = useState(false);\n  const audioRef = useRef(null);\n  const fetchHoroscope = useCallback(async (forceRefresh = false) => {\n    try {\n      // Track zodiac page view\n      analytics.trackZodiacView(sign.id);\n      if (forceRefresh) {\n        setRefreshing(true);\n        analytics.trackEvent('horoscope_refresh', {\n          event_category: 'user_action',\n          zodiac_sign: sign.id\n        });\n      } else {\n        setLoading(true);\n      }\n      setError('');\n\n      // Check cache first (skip cache if force refresh)\n      if (!forceRefresh) {\n        const cachedHoroscope = HoroscopeService.getCachedHoroscope(sign.id);\n        if (cachedHoroscope) {\n          setHoroscope(cachedHoroscope);\n          setLastUpdated(new Date());\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Fetch structured horoscope data from the new API\n      const horoscopeData = await HoroscopeService.getHoroscope(sign.id, forceRefresh);\n\n      // Check if we got structured data\n      if (horoscopeData && horoscopeData.categories) {\n        // Convert API response to the format expected by the display component\n        const categoryConfig = {\n          love: {\n            id: 'love',\n            title: 'ආදරය සහ සම්බන්ධතා',\n            emoji: '💕'\n          },\n          career: {\n            id: 'career',\n            title: 'වෘත්තීය ජීවිතය',\n            emoji: '💼'\n          },\n          health: {\n            id: 'health',\n            title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n            emoji: '🌿'\n          },\n          finance: {\n            id: 'finance',\n            title: 'මූල්‍ය කටයුතු',\n            emoji: '💰'\n          },\n          general: {\n            id: 'general',\n            title: 'සාමාන්‍ය උපදෙස්',\n            emoji: '✨'\n          }\n        };\n        const categories = Object.entries(horoscopeData.categories).map(([key, content]) => ({\n          ...categoryConfig[key],\n          content: content || 'අද දිනය සඳහා විශේෂ තොරතුරු නොමැත.'\n        }));\n        setHoroscope({\n          categories,\n          structured: true,\n          dateCreated: horoscopeData.date_created,\n          createdAt: horoscopeData.created_at,\n          rawContent: horoscopeData.raw_content\n        });\n      } else {\n        // Fallback to old parsing method if we get raw text\n        setHoroscope(horoscopeData);\n      }\n      setLastUpdated(new Date());\n\n      // Track successful horoscope load\n      analytics.trackEvent('horoscope_loaded', {\n        event_category: 'content_interaction',\n        zodiac_sign: sign.id,\n        load_type: forceRefresh ? 'refresh' : 'initial',\n        content_length: typeof horoscopeData === 'string' ? horoscopeData.length : JSON.stringify(horoscopeData).length\n      });\n\n      // Cache the result\n      HoroscopeService.setCachedHoroscope(sign.id, horoscopeData);\n    } catch (err) {\n      setError(uiTexts.loading || 'රාශිඵල ලබා ගැනීමේදී දෝෂයක් ඇති විය. කරුණාකර නැවත උත්සාහ කරන්න.');\n      console.error('Error fetching horoscope:', err);\n\n      // Track error\n      analytics.trackError(`Horoscope fetch error: ${err.message}`, `ZodiacPage-${sign.id}`, false);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [sign.id]);\n  useEffect(() => {\n    fetchHoroscope();\n  }, [fetchHoroscope]);\n\n  // Immediate audio play attempt on component mount\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (audioRef.current && soundEnabled && !userInteracted) {\n        audioRef.current.loop = true;\n        audioRef.current.volume = 0.3;\n\n        // Multiple attempts to play\n        const attemptPlay = () => {\n          const playPromise = audioRef.current.play();\n          if (playPromise !== undefined) {\n            playPromise.then(() => {\n              setUserInteracted(true);\n              setShowAudioPrompt(false);\n            }).catch(() => {\n              // If first attempt fails, show prompt\n              setShowAudioPrompt(true);\n            });\n          }\n        };\n        attemptPlay();\n      }\n    }, 500); // Small delay to ensure component is fully mounted\n\n    return () => clearTimeout(timer);\n  }, [soundEnabled, userInteracted]); // Include dependencies\n\n  // Auto-play background music when component mounts or user interacts\n  useEffect(() => {\n    if (audioRef.current && soundEnabled) {\n      // Set audio properties\n      audioRef.current.loop = true;\n      audioRef.current.volume = 0.3; // Set volume to 30%\n\n      // Try to play the audio\n      const playPromise = audioRef.current.play();\n      if (playPromise !== undefined) {\n        playPromise.then(() => {\n          setUserInteracted(true);\n          setShowAudioPrompt(false);\n        }).catch(error => {\n          // Show prompt to encourage user interaction\n          setShowAudioPrompt(true);\n        });\n      }\n    }\n  }, [soundEnabled, userInteracted]);\n\n  // Add user interaction listeners to enable audio\n  useEffect(() => {\n    const handleUserInteraction = () => {\n      if (!userInteracted && audioRef.current && soundEnabled) {\n        const playPromise = audioRef.current.play();\n        if (playPromise !== undefined) {\n          playPromise.then(() => {\n            setUserInteracted(true);\n            setShowAudioPrompt(false);\n          }).catch(console.error);\n        }\n      }\n    };\n\n    // Add event listeners for user interaction\n    const events = ['click', 'touchstart', 'keydown', 'scroll'];\n    events.forEach(event => {\n      document.addEventListener(event, handleUserInteraction, {\n        once: true\n      });\n    });\n\n    // Cleanup\n    return () => {\n      events.forEach(event => {\n        document.removeEventListener(event, handleUserInteraction);\n      });\n    };\n  }, [soundEnabled, userInteracted]);\n\n  // Translation effect - translate horoscope when language changes\n  useEffect(() => {\n    const translateHoroscope = async () => {\n      if (!horoscope || !horoscope.categories || currentLanguage === 'si') {\n        // If no horoscope or already in Sinhala (original language), no translation needed\n        setTranslatedHoroscope(null);\n        return;\n      }\n      try {\n        // Extract categories for translation\n        const categoriesToTranslate = {};\n        horoscope.categories.forEach(category => {\n          if (category.content) {\n            categoriesToTranslate[category.id] = category.content;\n          }\n        });\n\n        // Translate the categories\n        const translatedCategories = await translateHoroscopeCategories(categoriesToTranslate, 'si');\n\n        // Create translated horoscope structure\n        const translatedCategoriesArray = horoscope.categories.map(category => ({\n          ...category,\n          content: translatedCategories[category.id] || category.content,\n          title: uiTexts[category.id] || category.title\n        }));\n        setTranslatedHoroscope({\n          ...horoscope,\n          categories: translatedCategoriesArray\n        });\n      } catch (error) {\n        console.error('Translation error:', error);\n        setTranslatedHoroscope(null);\n      }\n    };\n    translateHoroscope();\n  }, [horoscope, currentLanguage, translateHoroscopeCategories, uiTexts]);\n  const handleRefresh = () => {\n    fetchHoroscope(true);\n  };\n  const toggleSound = () => {\n    const newSoundState = !soundEnabled;\n    setSoundEnabled(newSoundState);\n\n    // Track sound toggle\n    analytics.trackEvent('sound_toggle', {\n      event_category: 'user_preference',\n      zodiac_sign: sign.id,\n      sound_enabled: newSoundState,\n      action: newSoundState ? 'enable' : 'disable'\n    });\n    if (audioRef.current) {\n      if (newSoundState) {\n        audioRef.current.play().then(() => {\n          setUserInteracted(true);\n          setShowAudioPrompt(false);\n        }).catch(console.error);\n      } else {\n        audioRef.current.pause();\n      }\n    }\n  };\n  const getCurrentDate = () => {\n    const today = new Date();\n    const options = {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      weekday: 'long'\n    };\n    return today.toLocaleDateString('si-LK', options);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"zodiac-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"audio\", {\n      ref: audioRef,\n      src: `/music.mp3?v=${window.CACHE_VERSION || Date.now()}`,\n      loop: true,\n      style: {\n        display: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 698,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divine-background\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: `/god.jpg?v=${window.CACHE_VERSION || Date.now()}`,\n        alt: \"Divine Blessing\",\n        className: \"god-image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 706,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ParticleBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 714,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SmokeAnimation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 715,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(KuberaAnimation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/\",\n      className: \"back-button\",\n      children: \"\\u2190 \\u0DB8\\u0DD4\\u0DBD\\u0DCA \\u0DB4\\u0DD2\\u0DA7\\u0DD4\\u0DC0\\u0DA7\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 718,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"zodiac-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"zodiac-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"zodiac-icon\",\n          style: {\n            fontSize: '5rem',\n            marginBottom: '1rem'\n          },\n          children: zodiacIcons[sign.id]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"zodiac-title\",\n          children: currentLanguage === 'en' ? sign.english : currentLanguage === 'ta' ? sign.tamil : sign.sinhala\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"zodiac-subtitle\",\n          children: currentLanguage === 'en' ? `${sign.english} Sign` : currentLanguage === 'ta' ? `${sign.tamil} ராசி` : `${sign.english} රාශිය`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 11\n        }, this), horoscope && horoscope.dateCreated ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'rgba(244, 208, 63, 0.15)',\n            border: '1px solid rgba(244, 208, 63, 0.3)',\n            borderRadius: '15px',\n            padding: '1rem',\n            marginBottom: '2rem',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#f4d03f',\n              fontSize: '1.1rem',\n              fontWeight: 'bold',\n              marginBottom: '0.5rem'\n            },\n            children: [\"\\uD83D\\uDCC5 \", uiTexts.horoscopeDate]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#ffffff',\n              fontSize: '1rem'\n            },\n            children: new Date(horoscope.dateCreated).toLocaleDateString('si-LK', {\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric',\n              weekday: 'long'\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 15\n          }, this), horoscope.createdAt && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#aeb6bf',\n              fontSize: '0.85rem',\n              marginTop: '0.5rem'\n            },\n            children: [\"\\u0DA2\\u0DB1\\u0DB1\\u0DBA \\u0D9A\\u0DC5\\u0DDA: \", new Date(horoscope.createdAt).toLocaleString('si-LK')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#aeb6bf',\n            marginBottom: '2rem'\n          },\n          children: getCurrentDate()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"horoscope-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"horoscope-title\",\n            style: {\n              margin: 0\n            },\n            children: uiTexts.todayHoroscope\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 11\n        }, this), lastUpdated && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.85rem',\n            color: '#aeb6bf',\n            marginBottom: '1rem',\n            textAlign: 'center',\n            fontStyle: 'italic'\n          },\n          children: [uiTexts.lastUpdated, \": \", lastUpdated.toLocaleTimeString('si-LK', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 13\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: uiTexts.loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 13\n        }, this), refreshing && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: uiTexts.refreshing\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 800,\n          columnNumber: 13\n        }, this), isTranslating && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: currentLanguage === 'en' ? 'Translating horoscope... Please wait.' : currentLanguage === 'ta' ? 'ராசிபலன் மொழிபெயர்க்கிறது... தயவுசெய்து காத்திருக்கவும்.' : 'රාශිඵල පරිවර්තනය කරමින්... කරුණාකර රැඳී සිටින්න.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error\",\n          children: [error, /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            style: {\n              marginLeft: '1rem',\n              background: 'rgba(231, 76, 60, 0.1)',\n              border: '1px solid #e74c3c',\n              color: '#e74c3c',\n              padding: '0.4rem 0.8rem',\n              borderRadius: '15px',\n              cursor: 'pointer',\n              fontSize: '0.8rem'\n            },\n            children: uiTexts.retry\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 13\n        }, this), !loading && !refreshing && !error && !isTranslating && (translatedHoroscope || horoscope) && /*#__PURE__*/_jsxDEV(StructuredHoroscopeDisplay, {\n          horoscope: translatedHoroscope || horoscope,\n          uiTexts: uiTexts\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 835,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 9\n      }, this), showAudioPrompt && soundEnabled && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: '20px',\n          right: '20px',\n          background: 'rgba(244, 208, 63, 0.95)',\n          color: '#1a1a2e',\n          padding: '1rem 1.5rem',\n          borderRadius: '15px',\n          border: '2px solid #f4d03f',\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',\n          zIndex: 1000,\n          animation: 'pulse 2s infinite',\n          cursor: 'pointer',\n          fontFamily: 'Noto Sans Sinhala, sans-serif',\n          fontWeight: 'bold',\n          fontSize: '0.9rem',\n          maxWidth: '250px',\n          textAlign: 'center'\n        },\n        onClick: () => {\n          if (audioRef.current) {\n            audioRef.current.play().then(() => {\n              setUserInteracted(true);\n              setShowAudioPrompt(false);\n            }).catch(console.error);\n          }\n        },\n        children: \"\\uD83C\\uDFB5 \\u0DC1\\u0DB6\\u0DCA\\u0DAF \\u0DC3\\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DD2\\u0DBA \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DA7 \\u0D9A\\u0DCA\\u0DBD\\u0DD2\\u0D9A\\u0DCA \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 841,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"controls\",\n        style: {\n          marginTop: '2rem',\n          display: 'flex',\n          gap: '1rem',\n          flexWrap: 'wrap',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleSound,\n          className: \"sound-toggle\",\n          style: {\n            background: soundEnabled ? 'rgba(244, 208, 63, 0.2)' : 'rgba(255, 255, 255, 0.1)',\n            border: '1px solid #f4d03f',\n            color: '#f4d03f',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '25px',\n            cursor: 'pointer',\n            fontFamily: 'Noto Sans Sinhala, sans-serif',\n            transition: 'all 0.3s ease'\n          },\n          children: soundEnabled ? '🔊 ශබ්දය නිශ්ශබ්ද කරන්න' : '🔇 ශබ්දය සක්‍රිය කරන්න'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 874,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 873,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spiritual-message\",\n        style: {\n          marginTop: '3rem',\n          padding: '2rem',\n          background: 'rgba(244, 208, 63, 0.1)',\n          borderRadius: '15px',\n          border: '1px solid rgba(244, 208, 63, 0.3)',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#f4d03f',\n            fontStyle: 'italic',\n            fontSize: '1.1rem'\n          },\n          children: \"\\\"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0D94\\u0DB6 \\u0DC3\\u0DB8\\u0D9F \\u0DC0\\u0DDA\\u0DC0\\u0DCF\\\"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 902,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 894,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 722,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 696,\n    columnNumber: 5\n  }, this);\n};\n_s(ZodiacPage, \"abbojTht2x2o9//oYdjdCY9vuyc=\", false, function () {\n  return [useLanguage, useAnalytics, useComponentTracking];\n});\n_c3 = ZodiacPage;\nexport default ZodiacPage;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"CategoryCard\");\n$RefreshReg$(_c2, \"StructuredHoroscopeDisplay\");\n$RefreshReg$(_c3, \"ZodiacPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Link", "ParticleBackground", "SmokeAnimation", "KuberaAnimation", "HoroscopeService", "useLanguage", "useAnalytics", "useComponentTracking", "jsxDEV", "_jsxDEV", "zodiacIcons", "aries", "taurus", "gemini", "cancer", "leo", "virgo", "libra", "scorpio", "sagittarius", "capricorn", "aquarius", "pisces", "parseHoroscopeIntoStructuredCategories", "rawText", "cleanText", "replace", "trim", "categories", "love", "id", "title", "emoji", "icon", "content", "keywords", "career", "health", "finance", "general", "lines", "split", "filter", "line", "length", "currentCategory", "contentBuffer", "i", "detectedCategory", "numberedMatch", "match", "num", "parseInt", "categoryOrder", "catId", "catData", "Object", "entries", "keyword", "toLowerCase", "includes", "join", "cleanContent", "RegExp", "push", "values", "some", "cat", "sentences", "s", "categoriesArray", "keys", "for<PERSON>ach", "sentence", "index", "categoryIndex", "categoryKey", "category", "substring", "genericContent", "map", "key", "CategoryCard", "cardStyles", "background", "border", "shadow", "style", "className", "marginBottom", "padding", "borderRadius", "boxShadow", "<PERSON><PERSON>ilter", "transition", "position", "overflow", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "children", "top", "right", "width", "height", "backgroundSize", "opacity", "pointerEvents", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "zIndex", "fontSize", "marginRight", "color", "margin", "fontFamily", "fontWeight", "textShadow", "marginTop", "lineHeight", "textAlign", "bottom", "left", "_c", "StructuredHoroscopeDisplay", "horoscope", "uiTexts", "structured", "Array", "isArray", "generatingHoroscope", "max<PERSON><PERSON><PERSON>", "_c2", "ZodiacPage", "sign", "_s", "setHoroscope", "loading", "setLoading", "error", "setError", "soundEnabled", "setSoundEnabled", "lastUpdated", "setLastUpdated", "translatedHoroscope", "setTranslatedHoroscope", "currentLanguage", "translateHoroscopeCategories", "isTranslating", "getUITexts", "analytics", "refreshing", "setRefreshing", "userInteracted", "setUserInteracted", "showAudioPrompt", "setShowAudioPrompt", "audioRef", "fetchHoroscope", "forceRefresh", "trackZodiacView", "trackEvent", "event_category", "zodiac_sign", "cachedHoroscope", "getCachedHoroscope", "Date", "horoscopeData", "getHoroscope", "categoryConfig", "dateCreated", "date_created", "createdAt", "created_at", "rawContent", "raw_content", "load_type", "content_length", "JSON", "stringify", "setCachedHoroscope", "err", "console", "trackError", "message", "timer", "setTimeout", "current", "loop", "volume", "attemptPlay", "playPromise", "play", "undefined", "then", "catch", "clearTimeout", "handleUserInteraction", "events", "event", "document", "addEventListener", "once", "removeEventListener", "translateHoroscope", "categoriesToTranslate", "translatedCategories", "translatedCategoriesArray", "handleRefresh", "toggleSound", "newSoundState", "sound_enabled", "action", "pause", "getCurrentDate", "today", "options", "year", "month", "day", "weekday", "toLocaleDateString", "ref", "src", "window", "CACHE_VERSION", "now", "alt", "to", "english", "tamil", "sinhala", "horoscopeDate", "toLocaleString", "todayHoroscope", "fontStyle", "toLocaleTimeString", "hour", "minute", "hour12", "onClick", "marginLeft", "cursor", "retry", "animation", "gap", "flexWrap", "justifyContent", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/ZodiacPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport SmokeAnimation from './SmokeAnimation';\nimport KuberaAnimation from './KuberaAnimation';\nimport HoroscopeService from '../services/HoroscopeService';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';\n\n\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\n\n// Advanced horoscope parser that extracts structured content\nconst parseHoroscopeIntoStructuredCategories = (rawText) => {\n  // Check if rawText is valid\n  if (!rawText || typeof rawText !== 'string') {\n    return [];\n  }\n  \n  // Clean the raw text first\n  const cleanText = rawText\n    .replace(/\\*\\*/g, '')\n    .replace(/##/g, '')\n    .replace(/\\*/g, '')\n    .replace(/\\[.*?\\]/g, '')\n    .trim();\n\n  const categories = {\n    love: {\n      id: 'love',\n      title: 'ආදරය සහ සම්බන්ධතා',\n      emoji: '💕',\n      icon: '❤️',\n      content: '',\n      keywords: ['ආදර', 'සම්බන්ධතා', 'ප්‍රේම', 'විවාහ', 'මිත්‍ර']\n    },\n    career: {\n      id: 'career',\n      title: 'වෘත්තීය ජීවිතය',\n      emoji: '💼',\n      icon: '🏢',\n      content: '',\n      keywords: ['වෘත්ති', 'කාර්', 'රැකියා', 'ව්‍යාපාර', 'සේවා']\n    },\n    health: {\n      id: 'health',\n      title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n      emoji: '🌿',\n      icon: '🏥',\n      content: '',\n      keywords: ['සෞඛ්', 'සෞඛ', 'යහපැවැත්ම', 'ශරීර', 'මානසික']\n    },\n    finance: {\n      id: 'finance',\n      title: 'මූල්‍ය කටයුතු',\n      emoji: '💰',\n      icon: '💳',\n      content: '',\n      keywords: ['මූල්', 'මුදල්', 'ආර්ථික', 'ආදායම', 'වියදම']\n    },\n    general: {\n      id: 'general',\n      title: 'සාමාන්‍ය උපදෙස්',\n      emoji: '✨',\n      icon: '🔮',\n      content: '',\n      keywords: ['සාමාන්', 'උපදෙස්', 'සාර්ථක', 'ජීවිත', 'දිනය']\n    }\n  };\n\n  // Split text into lines and process\n  const lines = cleanText.split('\\n').filter(line => line.trim().length > 0);\n  let currentCategory = null;\n  let contentBuffer = [];\n  \n  // If no clear structure, distribute content evenly across categories\n  if (lines.length < 5) {\n    // Short content - put everything in general\n    categories.general.content = cleanText;\n  } else {\n    // Process each line to categorize content\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i].trim();\n      \n      if (!line || line.length < 2) continue;\n      \n      // Detect category by keywords or numbered sections\n      let detectedCategory = null;\n      \n      // Check for numbered sections (1., 2., 3., etc.)\n      const numberedMatch = line.match(/^(\\d+)\\./); \n      if (numberedMatch) {\n        const num = parseInt(numberedMatch[1]);\n        const categoryOrder = ['love', 'career', 'health', 'finance', 'general'];\n        if (num >= 1 && num <= 5) {\n          detectedCategory = categoryOrder[num - 1];\n        }\n      }\n      \n      // Check for keyword-based detection (more flexible)\n      if (!detectedCategory) {\n        for (const [catId, catData] of Object.entries(categories)) {\n          for (const keyword of catData.keywords) {\n            if (line.toLowerCase().includes(keyword.toLowerCase())) {\n              detectedCategory = catId;\n              break;\n            }\n          }\n          if (detectedCategory) break;\n        }\n      }\n      \n      // If we found a new category, save previous content\n      if (detectedCategory && detectedCategory !== currentCategory) {\n        if (currentCategory && contentBuffer.length > 0) {\n          categories[currentCategory].content = contentBuffer.join(' ').trim();\n        }\n        currentCategory = detectedCategory;\n        contentBuffer = [];\n        \n        // Clean the line and add to buffer\n        let cleanContent = line\n          .replace(/^\\d+\\.\\s*/, '')\n          .replace(/^[•-]\\s*/, '')\n          .replace(new RegExp(categories[detectedCategory].title, 'gi'), '')\n          .replace(/:/g, '')\n          .trim();\n        \n        if (cleanContent.length > 0) {\n          contentBuffer.push(cleanContent);\n        }\n      } else if (currentCategory) {\n        // Add content to current category\n        let cleanContent = line.trim();\n        if (cleanContent.length > 0) {\n          contentBuffer.push(cleanContent);\n        }\n      } else {\n        // No category detected yet, start with general and add content\n        currentCategory = 'general';\n        contentBuffer.push(line.trim());\n       }\n    }\n    \n    // If no categories were detected, distribute content intelligently\n    if (!Object.values(categories).some(cat => cat.content)) {\n      const sentences = cleanText.split(/[.!?]/).filter(s => s.trim().length > 10);\n      const categoriesArray = Object.keys(categories);\n      \n      sentences.forEach((sentence, index) => {\n        const categoryIndex = index % categoriesArray.length;\n        const categoryKey = categoriesArray[categoryIndex];\n        if (!categories[categoryKey].content) {\n          categories[categoryKey].content = sentence.trim();\n        } else {\n          categories[categoryKey].content += ' ' + sentence.trim();\n        }\n      });\n    }\n   }\n   \n   // Save final category content\n   if (currentCategory && contentBuffer.length > 0) {\n     categories[currentCategory].content = contentBuffer.join(' ').trim();\n   }\n   \n   // Ensure all categories have meaningful content\n  Object.values(categories).forEach((category, index) => {\n    if (!category.content || category.content.length < 5) {\n      // If still no content, use a portion of the original text\n      const sentences = cleanText.split(/[.!?]/).filter(s => s.trim().length > 5);\n      if (sentences.length > index) {\n        category.content = sentences[index].trim() || cleanText.substring(index * 50, (index + 1) * 50).trim();\n      } else {\n        // Last resort - use generic content based on category\n        const genericContent = {\n          love: 'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n          career: 'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.',\n          health: 'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.',\n          finance: 'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.',\n          general: 'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'\n        };\n        category.content = genericContent[category.id] || 'ධනාත්මක වෙනස්කම් සහ සාර්ථකත්වය අපේක්ෂා කරන්න.';\n      }\n    }\n  });\n   \n   // Ensure each category has its id properly set and return as array\n   return Object.entries(categories).map(([key, category]) => ({\n     ...category,\n     id: key // Ensure id is properly set\n   }));\n };\n \n // Beautiful category card component\n const CategoryCard = ({ category, index }) => {\n   const cardStyles = {\n     love: {\n       background: 'linear-gradient(135deg, rgba(255, 182, 193, 0.1) 0%, rgba(255, 105, 180, 0.05) 100%)',\n       border: '1px solid rgba(255, 182, 193, 0.3)',\n       shadow: '0 8px 32px rgba(255, 105, 180, 0.1)'\n     },\n     career: {\n       background: 'linear-gradient(135deg, rgba(70, 130, 180, 0.1) 0%, rgba(30, 144, 255, 0.05) 100%)',\n       border: '1px solid rgba(70, 130, 180, 0.3)',\n       shadow: '0 8px 32px rgba(30, 144, 255, 0.1)'\n     },\n     health: {\n       background: 'linear-gradient(135deg, rgba(144, 238, 144, 0.1) 0%, rgba(50, 205, 50, 0.05) 100%)',\n       border: '1px solid rgba(144, 238, 144, 0.3)',\n       shadow: '0 8px 32px rgba(50, 205, 50, 0.1)'\n     },\n     finance: {\n       background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%)',\n       border: '1px solid rgba(255, 215, 0, 0.3)',\n       shadow: '0 8px 32px rgba(255, 165, 0, 0.1)'\n     },\n     general: {\n       background: 'linear-gradient(135deg, rgba(221, 160, 221, 0.1) 0%, rgba(147, 112, 219, 0.05) 100%)',\n       border: '1px solid rgba(221, 160, 221, 0.3)',\n       shadow: '0 8px 32px rgba(147, 112, 219, 0.1)'\n     }\n   };\n   \n   const style = cardStyles[category.id] || cardStyles.general;\n   \n   return (\n     <div \n       className=\"horoscope-category-card\"\n       style={{\n         marginBottom: '2rem',\n         padding: '2rem',\n         background: style.background,\n         border: style.border,\n         borderRadius: '20px',\n         boxShadow: style.shadow,\n         backdropFilter: 'blur(10px)',\n         transition: 'all 0.3s ease',\n         position: 'relative',\n         overflow: 'hidden'\n       }}\n       onMouseEnter={(e) => {\n         e.currentTarget.style.transform = 'translateY(-5px)';\n         e.currentTarget.style.boxShadow = style.shadow.replace('0.1)', '0.2)');\n       }}\n       onMouseLeave={(e) => {\n         e.currentTarget.style.transform = 'translateY(0)';\n         e.currentTarget.style.boxShadow = style.shadow;\n       }}\n     >\n       {/* Decorative background pattern */}\n       <div \n         style={{\n           position: 'absolute',\n           top: '-50%',\n           right: '-50%',\n           width: '200%',\n           height: '200%',\n           background: `radial-gradient(circle, ${style.border.replace('1px solid ', '').replace('0.3)', '0.05)')} 1px, transparent 1px)`,\n           backgroundSize: '20px 20px',\n           opacity: 0.3,\n           pointerEvents: 'none'\n         }}\n       />\n       \n       {/* Header */}\n       <div \n         style={{\n           display: 'flex',\n           alignItems: 'center',\n           marginBottom: '1.5rem',\n           position: 'relative',\n           zIndex: 1\n         }}\n       >\n         <div \n           style={{\n             fontSize: '2.5rem',\n             marginRight: '1rem',\n             filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n           }}\n         >\n           {category.emoji}\n         </div>\n         <div>\n           <h3 \n             style={{\n               color: '#f4d03f',\n               fontSize: '1.4rem',\n               margin: 0,\n               fontFamily: 'Noto Sans Sinhala, sans-serif',\n               fontWeight: '600',\n               textShadow: '0 2px 4px rgba(0,0,0,0.1)'\n             }}\n           >\n             {category.title}\n           </h3>\n           <div \n             style={{\n               width: '50px',\n               height: '3px',\n               background: 'linear-gradient(90deg, #f4d03f, transparent)',\n               marginTop: '0.5rem',\n               borderRadius: '2px'\n             }}\n           />\n         </div>\n       </div>\n       \n       {/* Content */}\n       <div \n         style={{\n           position: 'relative',\n           zIndex: 1\n         }}\n       >\n         <p \n           style={{\n             color: '#e8f4fd',\n             lineHeight: '1.8',\n             fontSize: '1.1rem',\n             margin: 0,\n             fontFamily: 'Noto Sans Sinhala, sans-serif',\n             textAlign: 'justify',\n             textShadow: '0 1px 2px rgba(0,0,0,0.1)'\n           }}\n         >\n           {category.content}\n         </p>\n       </div>\n       \n       {/* Bottom accent */}\n       <div \n         style={{\n           position: 'absolute',\n           bottom: 0,\n           left: 0,\n           right: 0,\n           height: '4px',\n           background: `linear-gradient(90deg, ${style.border.replace('1px solid ', '').replace('0.3)', '0.6)')}, transparent)`,\n           borderRadius: '0 0 20px 20px'\n         }}\n       />\n     </div>\n   );\n };\n \n // Main display component\nconst StructuredHoroscopeDisplay = ({ horoscope, uiTexts }) => {\n  let categories;\n  \n  // Check if we have structured data from the new API\n  if (horoscope && horoscope.structured && horoscope.categories) {\n    // Handle both object and array formats\n    if (Array.isArray(horoscope.categories)) {\n      categories = horoscope.categories;\n    } else {\n      // Convert object to array with proper ids\n      categories = Object.entries(horoscope.categories).map(([key, category]) => ({\n        ...category,\n        id: category.id || key\n      }));\n    }\n  } else if (typeof horoscope === 'string') {\n    // Fallback to parsing raw text\n    categories = parseHoroscopeIntoStructuredCategories(horoscope);\n  } else {\n    categories = [];\n  }\n  \n  // Fallback if no categories found\n  if (!categories || categories.length === 0) {\n    return (\n      <div style={{\n        textAlign: 'center',\n        padding: '2rem',\n        color: '#e8f4fd',\n        fontFamily: 'Noto Sans Sinhala, sans-serif'\n      }}>\n        {uiTexts?.generatingHoroscope || 'රාශිඵල සකස් කරමින්... කරුණාකර රැඳී සිටින්න.'}\n      </div>\n    );\n  }\n  \n  return (\n    <div \n      className=\"structured-horoscope-display\"\n      style={{\n        maxWidth: '800px',\n        margin: '0 auto',\n        padding: '1rem'\n      }}\n    >\n      {categories.map((category, index) => (\n        <CategoryCard \n          key={category.id || `category-${index}`} \n          category={category} \n          index={index} \n        />\n      ))}\n    </div>\n  );\n};\n\nconst ZodiacPage = ({ sign }) => {\n  const [horoscope, setHoroscope] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [soundEnabled, setSoundEnabled] = useState(true); // Default to true (on)\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [translatedHoroscope, setTranslatedHoroscope] = useState(null);\n\n  // Language context\n  const {\n    currentLanguage,\n    translateHoroscopeCategories,\n    isTranslating,\n    getUITexts\n  } = useLanguage();\n\n  const uiTexts = getUITexts();\n\n  // Analytics integration\n  const analytics = useAnalytics();\n  useComponentTracking('ZodiacPage');\n  const [refreshing, setRefreshing] = useState(false);\n  const [userInteracted, setUserInteracted] = useState(false);\n  const [showAudioPrompt, setShowAudioPrompt] = useState(false);\n  const audioRef = useRef(null);\n\n  const fetchHoroscope = useCallback(async (forceRefresh = false) => {\n    try {\n      // Track zodiac page view\n      analytics.trackZodiacView(sign.id);\n\n      if (forceRefresh) {\n        setRefreshing(true);\n        analytics.trackEvent('horoscope_refresh', {\n          event_category: 'user_action',\n          zodiac_sign: sign.id\n        });\n      } else {\n        setLoading(true);\n      }\n      setError('');\n      \n      // Check cache first (skip cache if force refresh)\n      if (!forceRefresh) {\n        const cachedHoroscope = HoroscopeService.getCachedHoroscope(sign.id);\n        if (cachedHoroscope) {\n          setHoroscope(cachedHoroscope);\n          setLastUpdated(new Date());\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Fetch structured horoscope data from the new API\n      const horoscopeData = await HoroscopeService.getHoroscope(sign.id, forceRefresh);\n      \n      // Check if we got structured data\n      if (horoscopeData && horoscopeData.categories) {\n        // Convert API response to the format expected by the display component\n        const categoryConfig = {\n          love: { id: 'love', title: 'ආදරය සහ සම්බන්ධතා', emoji: '💕' },\n          career: { id: 'career', title: 'වෘත්තීය ජීවිතය', emoji: '💼' },\n          health: { id: 'health', title: 'සෞඛ්‍ය සහ යහපැවැත්ම', emoji: '🌿' },\n          finance: { id: 'finance', title: 'මූල්‍ය කටයුතු', emoji: '💰' },\n          general: { id: 'general', title: 'සාමාන්‍ය උපදෙස්', emoji: '✨' }\n        };\n        \n        const categories = Object.entries(horoscopeData.categories).map(([key, content]) => ({\n          ...categoryConfig[key],\n          content: content || 'අද දිනය සඳහා විශේෂ තොරතුරු නොමැත.'\n        }));\n        \n        setHoroscope({ \n          categories, \n          structured: true,\n          dateCreated: horoscopeData.date_created,\n          createdAt: horoscopeData.created_at,\n          rawContent: horoscopeData.raw_content\n        });\n      } else {\n        // Fallback to old parsing method if we get raw text\n        setHoroscope(horoscopeData);\n      }\n      \n      setLastUpdated(new Date());\n\n      // Track successful horoscope load\n      analytics.trackEvent('horoscope_loaded', {\n        event_category: 'content_interaction',\n        zodiac_sign: sign.id,\n        load_type: forceRefresh ? 'refresh' : 'initial',\n        content_length: typeof horoscopeData === 'string' ? horoscopeData.length : JSON.stringify(horoscopeData).length\n      });\n\n      // Cache the result\n      HoroscopeService.setCachedHoroscope(sign.id, horoscopeData);\n\n    } catch (err) {\n      setError(uiTexts.loading || 'රාශිඵල ලබා ගැනීමේදී දෝෂයක් ඇති විය. කරුණාකර නැවත උත්සාහ කරන්න.');\n      console.error('Error fetching horoscope:', err);\n\n      // Track error\n      analytics.trackError(\n        `Horoscope fetch error: ${err.message}`,\n        `ZodiacPage-${sign.id}`,\n        false\n      );\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [sign.id]);\n\n  useEffect(() => {\n    fetchHoroscope();\n  }, [fetchHoroscope]);\n\n  // Immediate audio play attempt on component mount\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (audioRef.current && soundEnabled && !userInteracted) {\n        audioRef.current.loop = true;\n        audioRef.current.volume = 0.3;\n\n        // Multiple attempts to play\n        const attemptPlay = () => {\n          const playPromise = audioRef.current.play();\n          if (playPromise !== undefined) {\n            playPromise.then(() => {\n              setUserInteracted(true);\n              setShowAudioPrompt(false);\n            }).catch(() => {\n              // If first attempt fails, show prompt\n              setShowAudioPrompt(true);\n            });\n          }\n        };\n\n        attemptPlay();\n      }\n    }, 500); // Small delay to ensure component is fully mounted\n\n    return () => clearTimeout(timer);\n  }, [soundEnabled, userInteracted]); // Include dependencies\n\n  // Auto-play background music when component mounts or user interacts\n  useEffect(() => {\n    if (audioRef.current && soundEnabled) {\n      // Set audio properties\n      audioRef.current.loop = true;\n      audioRef.current.volume = 0.3; // Set volume to 30%\n\n      // Try to play the audio\n      const playPromise = audioRef.current.play();\n      if (playPromise !== undefined) {\n        playPromise.then(() => {\n          setUserInteracted(true);\n          setShowAudioPrompt(false);\n        }).catch(error => {\n          // Show prompt to encourage user interaction\n          setShowAudioPrompt(true);\n        });\n      }\n    }\n  }, [soundEnabled, userInteracted]);\n\n  // Add user interaction listeners to enable audio\n  useEffect(() => {\n    const handleUserInteraction = () => {\n      if (!userInteracted && audioRef.current && soundEnabled) {\n        const playPromise = audioRef.current.play();\n        if (playPromise !== undefined) {\n          playPromise.then(() => {\n            setUserInteracted(true);\n            setShowAudioPrompt(false);\n          }).catch(console.error);\n        }\n      }\n    };\n\n    // Add event listeners for user interaction\n    const events = ['click', 'touchstart', 'keydown', 'scroll'];\n    events.forEach(event => {\n      document.addEventListener(event, handleUserInteraction, { once: true });\n    });\n\n    // Cleanup\n    return () => {\n      events.forEach(event => {\n        document.removeEventListener(event, handleUserInteraction);\n      });\n    };\n  }, [soundEnabled, userInteracted]);\n\n  // Translation effect - translate horoscope when language changes\n  useEffect(() => {\n    const translateHoroscope = async () => {\n      if (!horoscope || !horoscope.categories || currentLanguage === 'si') {\n        // If no horoscope or already in Sinhala (original language), no translation needed\n        setTranslatedHoroscope(null);\n        return;\n      }\n\n      try {\n        // Extract categories for translation\n        const categoriesToTranslate = {};\n        horoscope.categories.forEach(category => {\n          if (category.content) {\n            categoriesToTranslate[category.id] = category.content;\n          }\n        });\n\n        // Translate the categories\n        const translatedCategories = await translateHoroscopeCategories(categoriesToTranslate, 'si');\n\n        // Create translated horoscope structure\n        const translatedCategoriesArray = horoscope.categories.map(category => ({\n          ...category,\n          content: translatedCategories[category.id] || category.content,\n          title: uiTexts[category.id] || category.title\n        }));\n\n        setTranslatedHoroscope({\n          ...horoscope,\n          categories: translatedCategoriesArray\n        });\n\n      } catch (error) {\n        console.error('Translation error:', error);\n        setTranslatedHoroscope(null);\n      }\n    };\n\n    translateHoroscope();\n  }, [horoscope, currentLanguage, translateHoroscopeCategories, uiTexts]);\n\n  const handleRefresh = () => {\n    fetchHoroscope(true);\n  };\n\n\n\n  const toggleSound = () => {\n    const newSoundState = !soundEnabled;\n    setSoundEnabled(newSoundState);\n\n    // Track sound toggle\n    analytics.trackEvent('sound_toggle', {\n      event_category: 'user_preference',\n      zodiac_sign: sign.id,\n      sound_enabled: newSoundState,\n      action: newSoundState ? 'enable' : 'disable'\n    });\n\n    if (audioRef.current) {\n      if (newSoundState) {\n        audioRef.current.play().then(() => {\n          setUserInteracted(true);\n          setShowAudioPrompt(false);\n        }).catch(console.error);\n      } else {\n        audioRef.current.pause();\n      }\n    }\n  };\n\n  const getCurrentDate = () => {\n    const today = new Date();\n    const options = { \n      year: 'numeric', \n      month: 'long', \n      day: 'numeric',\n      weekday: 'long'\n    };\n    return today.toLocaleDateString('si-LK', options);\n  };\n\n  return (\n    <div className=\"zodiac-page\">\n      {/* Background Music Audio Element */}\n      <audio \n        ref={audioRef}\n        src={`/music.mp3?v=${window.CACHE_VERSION || Date.now()}`}\n        loop\n        style={{ display: 'none' }}\n      />\n      \n      {/* Divine Background Image */}\n      <div className=\"divine-background\">\n        <img \n          src={`/god.jpg?v=${window.CACHE_VERSION || Date.now()}`}\n          alt=\"Divine Blessing\" \n          className=\"god-image\"\n        />\n      </div>\n      \n      <ParticleBackground />\n      <SmokeAnimation />\n      <KuberaAnimation />\n      \n      <Link to=\"/\" className=\"back-button\">\n        ← මුල් පිටුවට\n      </Link>\n\n      <div className=\"zodiac-content\">\n        <div className=\"zodiac-header\">\n          <div className=\"zodiac-icon\" style={{ fontSize: '5rem', marginBottom: '1rem' }}>\n            {zodiacIcons[sign.id]}\n          </div>\n          <h1 className=\"zodiac-title\">\n            {currentLanguage === 'en' ? sign.english :\n             currentLanguage === 'ta' ? sign.tamil :\n             sign.sinhala}\n          </h1>\n          <h2 className=\"zodiac-subtitle\">\n            {currentLanguage === 'en' ? `${sign.english} Sign` :\n             currentLanguage === 'ta' ? `${sign.tamil} ராசி` :\n             `${sign.english} රාශිය`}\n          </h2>\n          \n          {/* Display horoscope date if available */}\n          {horoscope && horoscope.dateCreated ? (\n            <div style={{ \n              background: 'rgba(244, 208, 63, 0.15)',\n              border: '1px solid rgba(244, 208, 63, 0.3)',\n              borderRadius: '15px',\n              padding: '1rem',\n              marginBottom: '2rem',\n              textAlign: 'center'\n            }}>\n              <div style={{ color: '#f4d03f', fontSize: '1.1rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>\n                📅 {uiTexts.horoscopeDate}\n              </div>\n              <div style={{ color: '#ffffff', fontSize: '1rem' }}>\n                {new Date(horoscope.dateCreated).toLocaleDateString('si-LK', {\n                  year: 'numeric',\n                  month: 'long',\n                  day: 'numeric',\n                  weekday: 'long'\n                })}\n              </div>\n              {horoscope.createdAt && (\n                <div style={{ color: '#aeb6bf', fontSize: '0.85rem', marginTop: '0.5rem' }}>\n                  ජනනය කළේ: {new Date(horoscope.createdAt).toLocaleString('si-LK')}\n                </div>\n              )}\n            </div>\n          ) : (\n            <p style={{ color: '#aeb6bf', marginBottom: '2rem' }}>\n              {getCurrentDate()}\n            </p>\n          )}\n        </div>\n\n        <div className=\"horoscope-section\">\n          <div style={{ marginBottom: '1.5rem' }}>\n            <h3 className=\"horoscope-title\" style={{ margin: 0 }}>{uiTexts.todayHoroscope}</h3>\n          </div>\n\n          {lastUpdated && (\n            <div style={{\n              fontSize: '0.85rem',\n              color: '#aeb6bf',\n              marginBottom: '1rem',\n              textAlign: 'center',\n              fontStyle: 'italic'\n            }}>\n              {uiTexts.lastUpdated}: {lastUpdated.toLocaleTimeString('si-LK', {\n                hour: '2-digit',\n                minute: '2-digit',\n                hour12: true\n              })}\n            </div>\n          )}\n\n          {loading && (\n            <div className=\"loading\">\n              {uiTexts.loading}\n            </div>\n          )}\n\n          {refreshing && (\n            <div className=\"loading\">\n              {uiTexts.refreshing}\n            </div>\n          )}\n\n          {isTranslating && (\n            <div className=\"loading\">\n              {currentLanguage === 'en' ? 'Translating horoscope... Please wait.' :\n               currentLanguage === 'ta' ? 'ராசிபலன் மொழிபெயர்க்கிறது... தயவுசெய்து காத்திருக்கவும்.' :\n               'රාශිඵල පරිවර්තනය කරමින්... කරුණාකර රැඳී සිටින්න.'}\n            </div>\n          )}\n\n          {error && (\n            <div className=\"error\">\n              {error}\n              <button\n                onClick={handleRefresh}\n                style={{\n                  marginLeft: '1rem',\n                  background: 'rgba(231, 76, 60, 0.1)',\n                  border: '1px solid #e74c3c',\n                  color: '#e74c3c',\n                  padding: '0.4rem 0.8rem',\n                  borderRadius: '15px',\n                  cursor: 'pointer',\n                  fontSize: '0.8rem'\n                }}\n              >\n                {uiTexts.retry}\n              </button>\n            </div>\n          )}\n\n          {!loading && !refreshing && !error && !isTranslating && (translatedHoroscope || horoscope) && (\n            <StructuredHoroscopeDisplay horoscope={translatedHoroscope || horoscope} uiTexts={uiTexts} />\n          )}\n        </div>\n\n        {/* Audio Prompt */}\n        {showAudioPrompt && soundEnabled && (\n          <div style={{\n            position: 'fixed',\n            top: '20px',\n            right: '20px',\n            background: 'rgba(244, 208, 63, 0.95)',\n            color: '#1a1a2e',\n            padding: '1rem 1.5rem',\n            borderRadius: '15px',\n            border: '2px solid #f4d03f',\n            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',\n            zIndex: 1000,\n            animation: 'pulse 2s infinite',\n            cursor: 'pointer',\n            fontFamily: 'Noto Sans Sinhala, sans-serif',\n            fontWeight: 'bold',\n            fontSize: '0.9rem',\n            maxWidth: '250px',\n            textAlign: 'center'\n          }}\n          onClick={() => {\n            if (audioRef.current) {\n              audioRef.current.play().then(() => {\n                setUserInteracted(true);\n                setShowAudioPrompt(false);\n              }).catch(console.error);\n            }\n          }}\n          >\n            🎵 ශබ්ද සක්‍රිය කිරීමට ක්ලික් කරන්න\n          </div>\n        )}\n\n        <div className=\"controls\" style={{ marginTop: '2rem', display: 'flex', gap: '1rem', flexWrap: 'wrap', justifyContent: 'center' }}>\n          <button\n            onClick={toggleSound}\n            className=\"sound-toggle\"\n            style={{\n              background: soundEnabled ? 'rgba(244, 208, 63, 0.2)' : 'rgba(255, 255, 255, 0.1)',\n              border: '1px solid #f4d03f',\n              color: '#f4d03f',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '25px',\n              cursor: 'pointer',\n              fontFamily: 'Noto Sans Sinhala, sans-serif',\n              transition: 'all 0.3s ease'\n            }}\n          >\n            {soundEnabled ? '🔊 ශබ්දය නිශ්ශබ්ද කරන්න' : '🔇 ශබ්දය සක්‍රිය කරන්න'}\n          </button>\n\n\n        </div>\n\n        <div className=\"spiritual-message\" style={{\n          marginTop: '3rem',\n          padding: '2rem',\n          background: 'rgba(244, 208, 63, 0.1)',\n          borderRadius: '15px',\n          border: '1px solid rgba(244, 208, 63, 0.3)',\n          textAlign: 'center'\n        }}>\n          <p style={{ color: '#f4d03f', fontStyle: 'italic', fontSize: '1.1rem' }}>\n            \"කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා\"\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ZodiacPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3E,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,GAAG,EAAE,GAAG;EACRC,KAAK,EAAE,GAAG;EACVC,KAAK,EAAE,GAAG;EACVC,OAAO,EAAE,GAAG;EACZC,WAAW,EAAE,GAAG;EAChBC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE;AACV,CAAC;;AAED;AACA,MAAMC,sCAAsC,GAAIC,OAAO,IAAK;EAC1D;EACA,IAAI,CAACA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC3C,OAAO,EAAE;EACX;;EAEA;EACA,MAAMC,SAAS,GAAGD,OAAO,CACtBE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CACvBC,IAAI,CAAC,CAAC;EAET,MAAMC,UAAU,GAAG;IACjBC,IAAI,EAAE;MACJC,EAAE,EAAE,MAAM;MACVC,KAAK,EAAE,mBAAmB;MAC1BC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ;IAC5D,CAAC;IACDC,MAAM,EAAE;MACNN,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM;IAC3D,CAAC;IACDE,MAAM,EAAE;MACNP,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,qBAAqB;MAC5BC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ;IACzD,CAAC;IACDG,OAAO,EAAE;MACPR,EAAE,EAAE,SAAS;MACbC,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;IACxD,CAAC;IACDI,OAAO,EAAE;MACPT,EAAE,EAAE,SAAS;MACbC,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,GAAG;MACVC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM;IAC1D;EACF,CAAC;;EAED;EACA,MAAMK,KAAK,GAAGf,SAAS,CAACgB,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAChB,IAAI,CAAC,CAAC,CAACiB,MAAM,GAAG,CAAC,CAAC;EAC1E,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAIC,aAAa,GAAG,EAAE;;EAEtB;EACA,IAAIN,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;IACpB;IACAhB,UAAU,CAACW,OAAO,CAACL,OAAO,GAAGT,SAAS;EACxC,CAAC,MAAM;IACL;IACA,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,CAACI,MAAM,EAAEG,CAAC,EAAE,EAAE;MACrC,MAAMJ,IAAI,GAAGH,KAAK,CAACO,CAAC,CAAC,CAACpB,IAAI,CAAC,CAAC;MAE5B,IAAI,CAACgB,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;;MAE9B;MACA,IAAII,gBAAgB,GAAG,IAAI;;MAE3B;MACA,MAAMC,aAAa,GAAGN,IAAI,CAACO,KAAK,CAAC,UAAU,CAAC;MAC5C,IAAID,aAAa,EAAE;QACjB,MAAME,GAAG,GAAGC,QAAQ,CAACH,aAAa,CAAC,CAAC,CAAC,CAAC;QACtC,MAAMI,aAAa,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;QACxE,IAAIF,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,EAAE;UACxBH,gBAAgB,GAAGK,aAAa,CAACF,GAAG,GAAG,CAAC,CAAC;QAC3C;MACF;;MAEA;MACA,IAAI,CAACH,gBAAgB,EAAE;QACrB,KAAK,MAAM,CAACM,KAAK,EAAEC,OAAO,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC7B,UAAU,CAAC,EAAE;UACzD,KAAK,MAAM8B,OAAO,IAAIH,OAAO,CAACpB,QAAQ,EAAE;YACtC,IAAIQ,IAAI,CAACgB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC,EAAE;cACtDX,gBAAgB,GAAGM,KAAK;cACxB;YACF;UACF;UACA,IAAIN,gBAAgB,EAAE;QACxB;MACF;;MAEA;MACA,IAAIA,gBAAgB,IAAIA,gBAAgB,KAAKH,eAAe,EAAE;QAC5D,IAAIA,eAAe,IAAIC,aAAa,CAACF,MAAM,GAAG,CAAC,EAAE;UAC/ChB,UAAU,CAACiB,eAAe,CAAC,CAACX,OAAO,GAAGY,aAAa,CAACe,IAAI,CAAC,GAAG,CAAC,CAAClC,IAAI,CAAC,CAAC;QACtE;QACAkB,eAAe,GAAGG,gBAAgB;QAClCF,aAAa,GAAG,EAAE;;QAElB;QACA,IAAIgB,YAAY,GAAGnB,IAAI,CACpBjB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CACxBA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CACvBA,OAAO,CAAC,IAAIqC,MAAM,CAACnC,UAAU,CAACoB,gBAAgB,CAAC,CAACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CACjEL,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CACjBC,IAAI,CAAC,CAAC;QAET,IAAImC,YAAY,CAAClB,MAAM,GAAG,CAAC,EAAE;UAC3BE,aAAa,CAACkB,IAAI,CAACF,YAAY,CAAC;QAClC;MACF,CAAC,MAAM,IAAIjB,eAAe,EAAE;QAC1B;QACA,IAAIiB,YAAY,GAAGnB,IAAI,CAAChB,IAAI,CAAC,CAAC;QAC9B,IAAImC,YAAY,CAAClB,MAAM,GAAG,CAAC,EAAE;UAC3BE,aAAa,CAACkB,IAAI,CAACF,YAAY,CAAC;QAClC;MACF,CAAC,MAAM;QACL;QACAjB,eAAe,GAAG,SAAS;QAC3BC,aAAa,CAACkB,IAAI,CAACrB,IAAI,CAAChB,IAAI,CAAC,CAAC,CAAC;MAChC;IACH;;IAEA;IACA,IAAI,CAAC6B,MAAM,CAACS,MAAM,CAACrC,UAAU,CAAC,CAACsC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACjC,OAAO,CAAC,EAAE;MACvD,MAAMkC,SAAS,GAAG3C,SAAS,CAACgB,KAAK,CAAC,OAAO,CAAC,CAACC,MAAM,CAAC2B,CAAC,IAAIA,CAAC,CAAC1C,IAAI,CAAC,CAAC,CAACiB,MAAM,GAAG,EAAE,CAAC;MAC5E,MAAM0B,eAAe,GAAGd,MAAM,CAACe,IAAI,CAAC3C,UAAU,CAAC;MAE/CwC,SAAS,CAACI,OAAO,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACrC,MAAMC,aAAa,GAAGD,KAAK,GAAGJ,eAAe,CAAC1B,MAAM;QACpD,MAAMgC,WAAW,GAAGN,eAAe,CAACK,aAAa,CAAC;QAClD,IAAI,CAAC/C,UAAU,CAACgD,WAAW,CAAC,CAAC1C,OAAO,EAAE;UACpCN,UAAU,CAACgD,WAAW,CAAC,CAAC1C,OAAO,GAAGuC,QAAQ,CAAC9C,IAAI,CAAC,CAAC;QACnD,CAAC,MAAM;UACLC,UAAU,CAACgD,WAAW,CAAC,CAAC1C,OAAO,IAAI,GAAG,GAAGuC,QAAQ,CAAC9C,IAAI,CAAC,CAAC;QAC1D;MACF,CAAC,CAAC;IACJ;EACD;;EAEA;EACA,IAAIkB,eAAe,IAAIC,aAAa,CAACF,MAAM,GAAG,CAAC,EAAE;IAC/ChB,UAAU,CAACiB,eAAe,CAAC,CAACX,OAAO,GAAGY,aAAa,CAACe,IAAI,CAAC,GAAG,CAAC,CAAClC,IAAI,CAAC,CAAC;EACtE;;EAEA;EACD6B,MAAM,CAACS,MAAM,CAACrC,UAAU,CAAC,CAAC4C,OAAO,CAAC,CAACK,QAAQ,EAAEH,KAAK,KAAK;IACrD,IAAI,CAACG,QAAQ,CAAC3C,OAAO,IAAI2C,QAAQ,CAAC3C,OAAO,CAACU,MAAM,GAAG,CAAC,EAAE;MACpD;MACA,MAAMwB,SAAS,GAAG3C,SAAS,CAACgB,KAAK,CAAC,OAAO,CAAC,CAACC,MAAM,CAAC2B,CAAC,IAAIA,CAAC,CAAC1C,IAAI,CAAC,CAAC,CAACiB,MAAM,GAAG,CAAC,CAAC;MAC3E,IAAIwB,SAAS,CAACxB,MAAM,GAAG8B,KAAK,EAAE;QAC5BG,QAAQ,CAAC3C,OAAO,GAAGkC,SAAS,CAACM,KAAK,CAAC,CAAC/C,IAAI,CAAC,CAAC,IAAIF,SAAS,CAACqD,SAAS,CAACJ,KAAK,GAAG,EAAE,EAAE,CAACA,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC/C,IAAI,CAAC,CAAC;MACxG,CAAC,MAAM;QACL;QACA,MAAMoD,cAAc,GAAG;UACrBlD,IAAI,EAAE,8DAA8D;UACpEO,MAAM,EAAE,yDAAyD;UACjEC,MAAM,EAAE,mDAAmD;UAC3DC,OAAO,EAAE,0DAA0D;UACnEC,OAAO,EAAE;QACX,CAAC;QACDsC,QAAQ,CAAC3C,OAAO,GAAG6C,cAAc,CAACF,QAAQ,CAAC/C,EAAE,CAAC,IAAI,+CAA+C;MACnG;IACF;EACF,CAAC,CAAC;;EAED;EACA,OAAO0B,MAAM,CAACC,OAAO,CAAC7B,UAAU,CAAC,CAACoD,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEJ,QAAQ,CAAC,MAAM;IAC1D,GAAGA,QAAQ;IACX/C,EAAE,EAAEmD,GAAG,CAAC;EACV,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,MAAMC,YAAY,GAAGA,CAAC;EAAEL,QAAQ;EAAEH;AAAM,CAAC,KAAK;EAC5C,MAAMS,UAAU,GAAG;IACjBtD,IAAI,EAAE;MACJuD,UAAU,EAAE,sFAAsF;MAClGC,MAAM,EAAE,oCAAoC;MAC5CC,MAAM,EAAE;IACV,CAAC;IACDlD,MAAM,EAAE;MACNgD,UAAU,EAAE,oFAAoF;MAChGC,MAAM,EAAE,mCAAmC;MAC3CC,MAAM,EAAE;IACV,CAAC;IACDjD,MAAM,EAAE;MACN+C,UAAU,EAAE,oFAAoF;MAChGC,MAAM,EAAE,oCAAoC;MAC5CC,MAAM,EAAE;IACV,CAAC;IACDhD,OAAO,EAAE;MACP8C,UAAU,EAAE,kFAAkF;MAC9FC,MAAM,EAAE,kCAAkC;MAC1CC,MAAM,EAAE;IACV,CAAC;IACD/C,OAAO,EAAE;MACP6C,UAAU,EAAE,sFAAsF;MAClGC,MAAM,EAAE,oCAAoC;MAC5CC,MAAM,EAAE;IACV;EACF,CAAC;EAED,MAAMC,KAAK,GAAGJ,UAAU,CAACN,QAAQ,CAAC/C,EAAE,CAAC,IAAIqD,UAAU,CAAC5C,OAAO;EAE3D,oBACE9B,OAAA;IACE+E,SAAS,EAAC,yBAAyB;IACnCD,KAAK,EAAE;MACLE,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfN,UAAU,EAAEG,KAAK,CAACH,UAAU;MAC5BC,MAAM,EAAEE,KAAK,CAACF,MAAM;MACpBM,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAEL,KAAK,CAACD,MAAM;MACvBO,cAAc,EAAE,YAAY;MAC5BC,UAAU,EAAE,eAAe;MAC3BC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;IACZ,CAAE;IACFC,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACa,SAAS,GAAG,kBAAkB;MACpDF,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACK,SAAS,GAAGL,KAAK,CAACD,MAAM,CAAC5D,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;IACxE,CAAE;IACF2E,YAAY,EAAGH,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACa,SAAS,GAAG,eAAe;MACjDF,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACK,SAAS,GAAGL,KAAK,CAACD,MAAM;IAChD,CAAE;IAAAgB,QAAA,gBAGF7F,OAAA;MACE8E,KAAK,EAAE;QACLQ,QAAQ,EAAE,UAAU;QACpBQ,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdtB,UAAU,EAAE,2BAA2BG,KAAK,CAACF,MAAM,CAAC3D,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,wBAAwB;QAC9HiF,cAAc,EAAE,WAAW;QAC3BC,OAAO,EAAE,GAAG;QACZC,aAAa,EAAE;MACjB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFxG,OAAA;MACE8E,KAAK,EAAE;QACL2B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpB1B,YAAY,EAAE,QAAQ;QACtBM,QAAQ,EAAE,UAAU;QACpBqB,MAAM,EAAE;MACV,CAAE;MAAAd,QAAA,gBAEF7F,OAAA;QACE8E,KAAK,EAAE;UACL8B,QAAQ,EAAE,QAAQ;UAClBC,WAAW,EAAE,MAAM;UACnB5E,MAAM,EAAE;QACV,CAAE;QAAA4D,QAAA,EAEDzB,QAAQ,CAAC7C;MAAK;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACNxG,OAAA;QAAA6F,QAAA,gBACE7F,OAAA;UACE8E,KAAK,EAAE;YACLgC,KAAK,EAAE,SAAS;YAChBF,QAAQ,EAAE,QAAQ;YAClBG,MAAM,EAAE,CAAC;YACTC,UAAU,EAAE,+BAA+B;YAC3CC,UAAU,EAAE,KAAK;YACjBC,UAAU,EAAE;UACd,CAAE;UAAArB,QAAA,EAEDzB,QAAQ,CAAC9C;QAAK;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACLxG,OAAA;UACE8E,KAAK,EAAE;YACLkB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,KAAK;YACbtB,UAAU,EAAE,8CAA8C;YAC1DwC,SAAS,EAAE,QAAQ;YACnBjC,YAAY,EAAE;UAChB;QAAE;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxG,OAAA;MACE8E,KAAK,EAAE;QACLQ,QAAQ,EAAE,UAAU;QACpBqB,MAAM,EAAE;MACV,CAAE;MAAAd,QAAA,eAEF7F,OAAA;QACE8E,KAAK,EAAE;UACLgC,KAAK,EAAE,SAAS;UAChBM,UAAU,EAAE,KAAK;UACjBR,QAAQ,EAAE,QAAQ;UAClBG,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE,+BAA+B;UAC3CK,SAAS,EAAE,SAAS;UACpBH,UAAU,EAAE;QACd,CAAE;QAAArB,QAAA,EAEDzB,QAAQ,CAAC3C;MAAO;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNxG,OAAA;MACE8E,KAAK,EAAE;QACLQ,QAAQ,EAAE,UAAU;QACpBgC,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPxB,KAAK,EAAE,CAAC;QACRE,MAAM,EAAE,KAAK;QACbtB,UAAU,EAAE,0BAA0BG,KAAK,CAACF,MAAM,CAAC3D,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,gBAAgB;QACpHiE,YAAY,EAAE;MAChB;IAAE;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAgB,EAAA,GAxJM/C,YAAY;AAyJnB,MAAMgD,0BAA0B,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAC7D,IAAIxG,UAAU;;EAEd;EACA,IAAIuG,SAAS,IAAIA,SAAS,CAACE,UAAU,IAAIF,SAAS,CAACvG,UAAU,EAAE;IAC7D;IACA,IAAI0G,KAAK,CAACC,OAAO,CAACJ,SAAS,CAACvG,UAAU,CAAC,EAAE;MACvCA,UAAU,GAAGuG,SAAS,CAACvG,UAAU;IACnC,CAAC,MAAM;MACL;MACAA,UAAU,GAAG4B,MAAM,CAACC,OAAO,CAAC0E,SAAS,CAACvG,UAAU,CAAC,CAACoD,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEJ,QAAQ,CAAC,MAAM;QAC1E,GAAGA,QAAQ;QACX/C,EAAE,EAAE+C,QAAQ,CAAC/C,EAAE,IAAImD;MACrB,CAAC,CAAC,CAAC;IACL;EACF,CAAC,MAAM,IAAI,OAAOkD,SAAS,KAAK,QAAQ,EAAE;IACxC;IACAvG,UAAU,GAAGL,sCAAsC,CAAC4G,SAAS,CAAC;EAChE,CAAC,MAAM;IACLvG,UAAU,GAAG,EAAE;EACjB;;EAEA;EACA,IAAI,CAACA,UAAU,IAAIA,UAAU,CAACgB,MAAM,KAAK,CAAC,EAAE;IAC1C,oBACEnC,OAAA;MAAK8E,KAAK,EAAE;QACVuC,SAAS,EAAE,QAAQ;QACnBpC,OAAO,EAAE,MAAM;QACf6B,KAAK,EAAE,SAAS;QAChBE,UAAU,EAAE;MACd,CAAE;MAAAnB,QAAA,EACC,CAAA8B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,mBAAmB,KAAI;IAA6C;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3E,CAAC;EAEV;EAEA,oBACExG,OAAA;IACE+E,SAAS,EAAC,8BAA8B;IACxCD,KAAK,EAAE;MACLkD,QAAQ,EAAE,OAAO;MACjBjB,MAAM,EAAE,QAAQ;MAChB9B,OAAO,EAAE;IACX,CAAE;IAAAY,QAAA,EAED1E,UAAU,CAACoD,GAAG,CAAC,CAACH,QAAQ,EAAEH,KAAK,kBAC9BjE,OAAA,CAACyE,YAAY;MAEXL,QAAQ,EAAEA,QAAS;MACnBH,KAAK,EAAEA;IAAM,GAFRG,QAAQ,CAAC/C,EAAE,IAAI,YAAY4C,KAAK,EAAE;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGxC,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACyB,GAAA,GAtDIR,0BAA0B;AAwDhC,MAAMS,UAAU,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACV,SAAS,EAAEW,YAAY,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmJ,OAAO,EAAEC,UAAU,CAAC,GAAGpJ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqJ,KAAK,EAAEC,QAAQ,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuJ,YAAY,EAAEC,eAAe,CAAC,GAAGxJ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACxD,MAAM,CAACyJ,WAAW,EAAEC,cAAc,CAAC,GAAG1J,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC2J,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5J,QAAQ,CAAC,IAAI,CAAC;;EAEpE;EACA,MAAM;IACJ6J,eAAe;IACfC,4BAA4B;IAC5BC,aAAa;IACbC;EACF,CAAC,GAAGvJ,WAAW,CAAC,CAAC;EAEjB,MAAM+H,OAAO,GAAGwB,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAMC,SAAS,GAAGvJ,YAAY,CAAC,CAAC;EAChCC,oBAAoB,CAAC,YAAY,CAAC;EAClC,MAAM,CAACuJ,UAAU,EAAEC,aAAa,CAAC,GAAGnK,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoK,cAAc,EAAEC,iBAAiB,CAAC,GAAGrK,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsK,eAAe,EAAEC,kBAAkB,CAAC,GAAGvK,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMwK,QAAQ,GAAGrK,MAAM,CAAC,IAAI,CAAC;EAE7B,MAAMsK,cAAc,GAAGvK,WAAW,CAAC,OAAOwK,YAAY,GAAG,KAAK,KAAK;IACjE,IAAI;MACF;MACAT,SAAS,CAACU,eAAe,CAAC3B,IAAI,CAAC9G,EAAE,CAAC;MAElC,IAAIwI,YAAY,EAAE;QAChBP,aAAa,CAAC,IAAI,CAAC;QACnBF,SAAS,CAACW,UAAU,CAAC,mBAAmB,EAAE;UACxCC,cAAc,EAAE,aAAa;UAC7BC,WAAW,EAAE9B,IAAI,CAAC9G;QACpB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLkH,UAAU,CAAC,IAAI,CAAC;MAClB;MACAE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,IAAI,CAACoB,YAAY,EAAE;QACjB,MAAMK,eAAe,GAAGvK,gBAAgB,CAACwK,kBAAkB,CAAChC,IAAI,CAAC9G,EAAE,CAAC;QACpE,IAAI6I,eAAe,EAAE;UACnB7B,YAAY,CAAC6B,eAAe,CAAC;UAC7BrB,cAAc,CAAC,IAAIuB,IAAI,CAAC,CAAC,CAAC;UAC1B7B,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACA,MAAM8B,aAAa,GAAG,MAAM1K,gBAAgB,CAAC2K,YAAY,CAACnC,IAAI,CAAC9G,EAAE,EAAEwI,YAAY,CAAC;;MAEhF;MACA,IAAIQ,aAAa,IAAIA,aAAa,CAAClJ,UAAU,EAAE;QAC7C;QACA,MAAMoJ,cAAc,GAAG;UACrBnJ,IAAI,EAAE;YAAEC,EAAE,EAAE,MAAM;YAAEC,KAAK,EAAE,mBAAmB;YAAEC,KAAK,EAAE;UAAK,CAAC;UAC7DI,MAAM,EAAE;YAAEN,EAAE,EAAE,QAAQ;YAAEC,KAAK,EAAE,gBAAgB;YAAEC,KAAK,EAAE;UAAK,CAAC;UAC9DK,MAAM,EAAE;YAAEP,EAAE,EAAE,QAAQ;YAAEC,KAAK,EAAE,qBAAqB;YAAEC,KAAK,EAAE;UAAK,CAAC;UACnEM,OAAO,EAAE;YAAER,EAAE,EAAE,SAAS;YAAEC,KAAK,EAAE,eAAe;YAAEC,KAAK,EAAE;UAAK,CAAC;UAC/DO,OAAO,EAAE;YAAET,EAAE,EAAE,SAAS;YAAEC,KAAK,EAAE,iBAAiB;YAAEC,KAAK,EAAE;UAAI;QACjE,CAAC;QAED,MAAMJ,UAAU,GAAG4B,MAAM,CAACC,OAAO,CAACqH,aAAa,CAAClJ,UAAU,CAAC,CAACoD,GAAG,CAAC,CAAC,CAACC,GAAG,EAAE/C,OAAO,CAAC,MAAM;UACnF,GAAG8I,cAAc,CAAC/F,GAAG,CAAC;UACtB/C,OAAO,EAAEA,OAAO,IAAI;QACtB,CAAC,CAAC,CAAC;QAEH4G,YAAY,CAAC;UACXlH,UAAU;UACVyG,UAAU,EAAE,IAAI;UAChB4C,WAAW,EAAEH,aAAa,CAACI,YAAY;UACvCC,SAAS,EAAEL,aAAa,CAACM,UAAU;UACnCC,UAAU,EAAEP,aAAa,CAACQ;QAC5B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAxC,YAAY,CAACgC,aAAa,CAAC;MAC7B;MAEAxB,cAAc,CAAC,IAAIuB,IAAI,CAAC,CAAC,CAAC;;MAE1B;MACAhB,SAAS,CAACW,UAAU,CAAC,kBAAkB,EAAE;QACvCC,cAAc,EAAE,qBAAqB;QACrCC,WAAW,EAAE9B,IAAI,CAAC9G,EAAE;QACpByJ,SAAS,EAAEjB,YAAY,GAAG,SAAS,GAAG,SAAS;QAC/CkB,cAAc,EAAE,OAAOV,aAAa,KAAK,QAAQ,GAAGA,aAAa,CAAClI,MAAM,GAAG6I,IAAI,CAACC,SAAS,CAACZ,aAAa,CAAC,CAAClI;MAC3G,CAAC,CAAC;;MAEF;MACAxC,gBAAgB,CAACuL,kBAAkB,CAAC/C,IAAI,CAAC9G,EAAE,EAAEgJ,aAAa,CAAC;IAE7D,CAAC,CAAC,OAAOc,GAAG,EAAE;MACZ1C,QAAQ,CAACd,OAAO,CAACW,OAAO,IAAI,gEAAgE,CAAC;MAC7F8C,OAAO,CAAC5C,KAAK,CAAC,2BAA2B,EAAE2C,GAAG,CAAC;;MAE/C;MACA/B,SAAS,CAACiC,UAAU,CAClB,0BAA0BF,GAAG,CAACG,OAAO,EAAE,EACvC,cAAcnD,IAAI,CAAC9G,EAAE,EAAE,EACvB,KACF,CAAC;IACH,CAAC,SAAS;MACRkH,UAAU,CAAC,KAAK,CAAC;MACjBe,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACnB,IAAI,CAAC9G,EAAE,CAAC,CAAC;EAEbjC,SAAS,CAAC,MAAM;IACdwK,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;;EAEpB;EACAxK,SAAS,CAAC,MAAM;IACd,MAAMmM,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7B,IAAI7B,QAAQ,CAAC8B,OAAO,IAAI/C,YAAY,IAAI,CAACa,cAAc,EAAE;QACvDI,QAAQ,CAAC8B,OAAO,CAACC,IAAI,GAAG,IAAI;QAC5B/B,QAAQ,CAAC8B,OAAO,CAACE,MAAM,GAAG,GAAG;;QAE7B;QACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;UACxB,MAAMC,WAAW,GAAGlC,QAAQ,CAAC8B,OAAO,CAACK,IAAI,CAAC,CAAC;UAC3C,IAAID,WAAW,KAAKE,SAAS,EAAE;YAC7BF,WAAW,CAACG,IAAI,CAAC,MAAM;cACrBxC,iBAAiB,CAAC,IAAI,CAAC;cACvBE,kBAAkB,CAAC,KAAK,CAAC;YAC3B,CAAC,CAAC,CAACuC,KAAK,CAAC,MAAM;cACb;cACAvC,kBAAkB,CAAC,IAAI,CAAC;YAC1B,CAAC,CAAC;UACJ;QACF,CAAC;QAEDkC,WAAW,CAAC,CAAC;MACf;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAMM,YAAY,CAACX,KAAK,CAAC;EAClC,CAAC,EAAE,CAAC7C,YAAY,EAAEa,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEpC;EACAnK,SAAS,CAAC,MAAM;IACd,IAAIuK,QAAQ,CAAC8B,OAAO,IAAI/C,YAAY,EAAE;MACpC;MACAiB,QAAQ,CAAC8B,OAAO,CAACC,IAAI,GAAG,IAAI;MAC5B/B,QAAQ,CAAC8B,OAAO,CAACE,MAAM,GAAG,GAAG,CAAC,CAAC;;MAE/B;MACA,MAAME,WAAW,GAAGlC,QAAQ,CAAC8B,OAAO,CAACK,IAAI,CAAC,CAAC;MAC3C,IAAID,WAAW,KAAKE,SAAS,EAAE;QAC7BF,WAAW,CAACG,IAAI,CAAC,MAAM;UACrBxC,iBAAiB,CAAC,IAAI,CAAC;UACvBE,kBAAkB,CAAC,KAAK,CAAC;QAC3B,CAAC,CAAC,CAACuC,KAAK,CAACzD,KAAK,IAAI;UAChB;UACAkB,kBAAkB,CAAC,IAAI,CAAC;QAC1B,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAAChB,YAAY,EAAEa,cAAc,CAAC,CAAC;;EAElC;EACAnK,SAAS,CAAC,MAAM;IACd,MAAM+M,qBAAqB,GAAGA,CAAA,KAAM;MAClC,IAAI,CAAC5C,cAAc,IAAII,QAAQ,CAAC8B,OAAO,IAAI/C,YAAY,EAAE;QACvD,MAAMmD,WAAW,GAAGlC,QAAQ,CAAC8B,OAAO,CAACK,IAAI,CAAC,CAAC;QAC3C,IAAID,WAAW,KAAKE,SAAS,EAAE;UAC7BF,WAAW,CAACG,IAAI,CAAC,MAAM;YACrBxC,iBAAiB,CAAC,IAAI,CAAC;YACvBE,kBAAkB,CAAC,KAAK,CAAC;UAC3B,CAAC,CAAC,CAACuC,KAAK,CAACb,OAAO,CAAC5C,KAAK,CAAC;QACzB;MACF;IACF,CAAC;;IAED;IACA,MAAM4D,MAAM,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,CAAC;IAC3DA,MAAM,CAACrI,OAAO,CAACsI,KAAK,IAAI;MACtBC,QAAQ,CAACC,gBAAgB,CAACF,KAAK,EAAEF,qBAAqB,EAAE;QAAEK,IAAI,EAAE;MAAK,CAAC,CAAC;IACzE,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXJ,MAAM,CAACrI,OAAO,CAACsI,KAAK,IAAI;QACtBC,QAAQ,CAACG,mBAAmB,CAACJ,KAAK,EAAEF,qBAAqB,CAAC;MAC5D,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACzD,YAAY,EAAEa,cAAc,CAAC,CAAC;;EAElC;EACAnK,SAAS,CAAC,MAAM;IACd,MAAMsN,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI,CAAChF,SAAS,IAAI,CAACA,SAAS,CAACvG,UAAU,IAAI6H,eAAe,KAAK,IAAI,EAAE;QACnE;QACAD,sBAAsB,CAAC,IAAI,CAAC;QAC5B;MACF;MAEA,IAAI;QACF;QACA,MAAM4D,qBAAqB,GAAG,CAAC,CAAC;QAChCjF,SAAS,CAACvG,UAAU,CAAC4C,OAAO,CAACK,QAAQ,IAAI;UACvC,IAAIA,QAAQ,CAAC3C,OAAO,EAAE;YACpBkL,qBAAqB,CAACvI,QAAQ,CAAC/C,EAAE,CAAC,GAAG+C,QAAQ,CAAC3C,OAAO;UACvD;QACF,CAAC,CAAC;;QAEF;QACA,MAAMmL,oBAAoB,GAAG,MAAM3D,4BAA4B,CAAC0D,qBAAqB,EAAE,IAAI,CAAC;;QAE5F;QACA,MAAME,yBAAyB,GAAGnF,SAAS,CAACvG,UAAU,CAACoD,GAAG,CAACH,QAAQ,KAAK;UACtE,GAAGA,QAAQ;UACX3C,OAAO,EAAEmL,oBAAoB,CAACxI,QAAQ,CAAC/C,EAAE,CAAC,IAAI+C,QAAQ,CAAC3C,OAAO;UAC9DH,KAAK,EAAEqG,OAAO,CAACvD,QAAQ,CAAC/C,EAAE,CAAC,IAAI+C,QAAQ,CAAC9C;QAC1C,CAAC,CAAC,CAAC;QAEHyH,sBAAsB,CAAC;UACrB,GAAGrB,SAAS;UACZvG,UAAU,EAAE0L;QACd,CAAC,CAAC;MAEJ,CAAC,CAAC,OAAOrE,KAAK,EAAE;QACd4C,OAAO,CAAC5C,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1CO,sBAAsB,CAAC,IAAI,CAAC;MAC9B;IACF,CAAC;IAED2D,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAChF,SAAS,EAAEsB,eAAe,EAAEC,4BAA4B,EAAEtB,OAAO,CAAC,CAAC;EAEvE,MAAMmF,aAAa,GAAGA,CAAA,KAAM;IAC1BlD,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAID,MAAMmD,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,aAAa,GAAG,CAACtE,YAAY;IACnCC,eAAe,CAACqE,aAAa,CAAC;;IAE9B;IACA5D,SAAS,CAACW,UAAU,CAAC,cAAc,EAAE;MACnCC,cAAc,EAAE,iBAAiB;MACjCC,WAAW,EAAE9B,IAAI,CAAC9G,EAAE;MACpB4L,aAAa,EAAED,aAAa;MAC5BE,MAAM,EAAEF,aAAa,GAAG,QAAQ,GAAG;IACrC,CAAC,CAAC;IAEF,IAAIrD,QAAQ,CAAC8B,OAAO,EAAE;MACpB,IAAIuB,aAAa,EAAE;QACjBrD,QAAQ,CAAC8B,OAAO,CAACK,IAAI,CAAC,CAAC,CAACE,IAAI,CAAC,MAAM;UACjCxC,iBAAiB,CAAC,IAAI,CAAC;UACvBE,kBAAkB,CAAC,KAAK,CAAC;QAC3B,CAAC,CAAC,CAACuC,KAAK,CAACb,OAAO,CAAC5C,KAAK,CAAC;MACzB,CAAC,MAAM;QACLmB,QAAQ,CAAC8B,OAAO,CAAC0B,KAAK,CAAC,CAAC;MAC1B;IACF;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAG,IAAIjD,IAAI,CAAC,CAAC;IACxB,MAAMkD,OAAO,GAAG;MACdC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,OAAO,EAAE;IACX,CAAC;IACD,OAAOL,KAAK,CAACM,kBAAkB,CAAC,OAAO,EAAEL,OAAO,CAAC;EACnD,CAAC;EAED,oBACEtN,OAAA;IAAK+E,SAAS,EAAC,aAAa;IAAAc,QAAA,gBAE1B7F,OAAA;MACE4N,GAAG,EAAEjE,QAAS;MACdkE,GAAG,EAAE,gBAAgBC,MAAM,CAACC,aAAa,IAAI3D,IAAI,CAAC4D,GAAG,CAAC,CAAC,EAAG;MAC1DtC,IAAI;MACJ5G,KAAK,EAAE;QAAE2B,OAAO,EAAE;MAAO;IAAE;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGFxG,OAAA;MAAK+E,SAAS,EAAC,mBAAmB;MAAAc,QAAA,eAChC7F,OAAA;QACE6N,GAAG,EAAE,cAAcC,MAAM,CAACC,aAAa,IAAI3D,IAAI,CAAC4D,GAAG,CAAC,CAAC,EAAG;QACxDC,GAAG,EAAC,iBAAiB;QACrBlJ,SAAS,EAAC;MAAW;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENxG,OAAA,CAACR,kBAAkB;MAAA6G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtBxG,OAAA,CAACP,cAAc;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBxG,OAAA,CAACN,eAAe;MAAA2G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEnBxG,OAAA,CAACT,IAAI;MAAC2O,EAAE,EAAC,GAAG;MAACnJ,SAAS,EAAC,aAAa;MAAAc,QAAA,EAAC;IAErC;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPxG,OAAA;MAAK+E,SAAS,EAAC,gBAAgB;MAAAc,QAAA,gBAC7B7F,OAAA;QAAK+E,SAAS,EAAC,eAAe;QAAAc,QAAA,gBAC5B7F,OAAA;UAAK+E,SAAS,EAAC,aAAa;UAACD,KAAK,EAAE;YAAE8B,QAAQ,EAAE,MAAM;YAAE5B,YAAY,EAAE;UAAO,CAAE;UAAAa,QAAA,EAC5E5F,WAAW,CAACkI,IAAI,CAAC9G,EAAE;QAAC;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACNxG,OAAA;UAAI+E,SAAS,EAAC,cAAc;UAAAc,QAAA,EACzBmD,eAAe,KAAK,IAAI,GAAGb,IAAI,CAACgG,OAAO,GACvCnF,eAAe,KAAK,IAAI,GAAGb,IAAI,CAACiG,KAAK,GACrCjG,IAAI,CAACkG;QAAO;UAAAhI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACLxG,OAAA;UAAI+E,SAAS,EAAC,iBAAiB;UAAAc,QAAA,EAC5BmD,eAAe,KAAK,IAAI,GAAG,GAAGb,IAAI,CAACgG,OAAO,OAAO,GACjDnF,eAAe,KAAK,IAAI,GAAG,GAAGb,IAAI,CAACiG,KAAK,OAAO,GAC/C,GAAGjG,IAAI,CAACgG,OAAO;QAAQ;UAAA9H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,EAGJkB,SAAS,IAAIA,SAAS,CAAC8C,WAAW,gBACjCxK,OAAA;UAAK8E,KAAK,EAAE;YACVH,UAAU,EAAE,0BAA0B;YACtCC,MAAM,EAAE,mCAAmC;YAC3CM,YAAY,EAAE,MAAM;YACpBD,OAAO,EAAE,MAAM;YACfD,YAAY,EAAE,MAAM;YACpBqC,SAAS,EAAE;UACb,CAAE;UAAAxB,QAAA,gBACA7F,OAAA;YAAK8E,KAAK,EAAE;cAAEgC,KAAK,EAAE,SAAS;cAAEF,QAAQ,EAAE,QAAQ;cAAEK,UAAU,EAAE,MAAM;cAAEjC,YAAY,EAAE;YAAS,CAAE;YAAAa,QAAA,GAAC,eAC7F,EAAC8B,OAAO,CAAC2G,aAAa;UAAA;YAAAjI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACNxG,OAAA;YAAK8E,KAAK,EAAE;cAAEgC,KAAK,EAAE,SAAS;cAAEF,QAAQ,EAAE;YAAO,CAAE;YAAAf,QAAA,EAChD,IAAIuE,IAAI,CAAC1C,SAAS,CAAC8C,WAAW,CAAC,CAACmD,kBAAkB,CAAC,OAAO,EAAE;cAC3DJ,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE,SAAS;cACdC,OAAO,EAAE;YACX,CAAC;UAAC;YAAArH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACLkB,SAAS,CAACgD,SAAS,iBAClB1K,OAAA;YAAK8E,KAAK,EAAE;cAAEgC,KAAK,EAAE,SAAS;cAAEF,QAAQ,EAAE,SAAS;cAAEO,SAAS,EAAE;YAAS,CAAE;YAAAtB,QAAA,GAAC,+CAChE,EAAC,IAAIuE,IAAI,CAAC1C,SAAS,CAACgD,SAAS,CAAC,CAAC6D,cAAc,CAAC,OAAO,CAAC;UAAA;YAAAlI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENxG,OAAA;UAAG8E,KAAK,EAAE;YAAEgC,KAAK,EAAE,SAAS;YAAE9B,YAAY,EAAE;UAAO,CAAE;UAAAa,QAAA,EAClDuH,cAAc,CAAC;QAAC;UAAA/G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENxG,OAAA;QAAK+E,SAAS,EAAC,mBAAmB;QAAAc,QAAA,gBAChC7F,OAAA;UAAK8E,KAAK,EAAE;YAAEE,YAAY,EAAE;UAAS,CAAE;UAAAa,QAAA,eACrC7F,OAAA;YAAI+E,SAAS,EAAC,iBAAiB;YAACD,KAAK,EAAE;cAAEiC,MAAM,EAAE;YAAE,CAAE;YAAAlB,QAAA,EAAE8B,OAAO,CAAC6G;UAAc;YAAAnI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,EAELoC,WAAW,iBACV5I,OAAA;UAAK8E,KAAK,EAAE;YACV8B,QAAQ,EAAE,SAAS;YACnBE,KAAK,EAAE,SAAS;YAChB9B,YAAY,EAAE,MAAM;YACpBqC,SAAS,EAAE,QAAQ;YACnBoH,SAAS,EAAE;UACb,CAAE;UAAA5I,QAAA,GACC8B,OAAO,CAACiB,WAAW,EAAC,IAAE,EAACA,WAAW,CAAC8F,kBAAkB,CAAC,OAAO,EAAE;YAC9DC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE,SAAS;YACjBC,MAAM,EAAE;UACV,CAAC,CAAC;QAAA;UAAAxI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEA8B,OAAO,iBACNtI,OAAA;UAAK+E,SAAS,EAAC,SAAS;UAAAc,QAAA,EACrB8B,OAAO,CAACW;QAAO;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CACN,EAEA6C,UAAU,iBACTrJ,OAAA;UAAK+E,SAAS,EAAC,SAAS;UAAAc,QAAA,EACrB8B,OAAO,CAAC0B;QAAU;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACN,EAEA0C,aAAa,iBACZlJ,OAAA;UAAK+E,SAAS,EAAC,SAAS;UAAAc,QAAA,EACrBmD,eAAe,KAAK,IAAI,GAAG,uCAAuC,GAClEA,eAAe,KAAK,IAAI,GAAG,0DAA0D,GACrF;QAAkD;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACN,EAEAgC,KAAK,iBACJxI,OAAA;UAAK+E,SAAS,EAAC,OAAO;UAAAc,QAAA,GACnB2C,KAAK,eACNxI,OAAA;YACE8O,OAAO,EAAEhC,aAAc;YACvBhI,KAAK,EAAE;cACLiK,UAAU,EAAE,MAAM;cAClBpK,UAAU,EAAE,wBAAwB;cACpCC,MAAM,EAAE,mBAAmB;cAC3BkC,KAAK,EAAE,SAAS;cAChB7B,OAAO,EAAE,eAAe;cACxBC,YAAY,EAAE,MAAM;cACpB8J,MAAM,EAAE,SAAS;cACjBpI,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,EAED8B,OAAO,CAACsH;UAAK;YAAA5I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEA,CAAC8B,OAAO,IAAI,CAACe,UAAU,IAAI,CAACb,KAAK,IAAI,CAACU,aAAa,KAAKJ,mBAAmB,IAAIpB,SAAS,CAAC,iBACxF1H,OAAA,CAACyH,0BAA0B;UAACC,SAAS,EAAEoB,mBAAmB,IAAIpB,SAAU;UAACC,OAAO,EAAEA;QAAQ;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC7F;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLiD,eAAe,IAAIf,YAAY,iBAC9B1I,OAAA;QAAK8E,KAAK,EAAE;UACVQ,QAAQ,EAAE,OAAO;UACjBQ,GAAG,EAAE,MAAM;UACXC,KAAK,EAAE,MAAM;UACbpB,UAAU,EAAE,0BAA0B;UACtCmC,KAAK,EAAE,SAAS;UAChB7B,OAAO,EAAE,aAAa;UACtBC,YAAY,EAAE,MAAM;UACpBN,MAAM,EAAE,mBAAmB;UAC3BO,SAAS,EAAE,+BAA+B;UAC1CwB,MAAM,EAAE,IAAI;UACZuI,SAAS,EAAE,mBAAmB;UAC9BF,MAAM,EAAE,SAAS;UACjBhI,UAAU,EAAE,+BAA+B;UAC3CC,UAAU,EAAE,MAAM;UAClBL,QAAQ,EAAE,QAAQ;UAClBoB,QAAQ,EAAE,OAAO;UACjBX,SAAS,EAAE;QACb,CAAE;QACFyH,OAAO,EAAEA,CAAA,KAAM;UACb,IAAInF,QAAQ,CAAC8B,OAAO,EAAE;YACpB9B,QAAQ,CAAC8B,OAAO,CAACK,IAAI,CAAC,CAAC,CAACE,IAAI,CAAC,MAAM;cACjCxC,iBAAiB,CAAC,IAAI,CAAC;cACvBE,kBAAkB,CAAC,KAAK,CAAC;YAC3B,CAAC,CAAC,CAACuC,KAAK,CAACb,OAAO,CAAC5C,KAAK,CAAC;UACzB;QACF,CAAE;QAAA3C,QAAA,EACD;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eAEDxG,OAAA;QAAK+E,SAAS,EAAC,UAAU;QAACD,KAAK,EAAE;UAAEqC,SAAS,EAAE,MAAM;UAAEV,OAAO,EAAE,MAAM;UAAE0I,GAAG,EAAE,MAAM;UAAEC,QAAQ,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAxJ,QAAA,eAC/H7F,OAAA;UACE8O,OAAO,EAAE/B,WAAY;UACrBhI,SAAS,EAAC,cAAc;UACxBD,KAAK,EAAE;YACLH,UAAU,EAAE+D,YAAY,GAAG,yBAAyB,GAAG,0BAA0B;YACjF9D,MAAM,EAAE,mBAAmB;YAC3BkC,KAAK,EAAE,SAAS;YAChB7B,OAAO,EAAE,eAAe;YACxBC,YAAY,EAAE,MAAM;YACpB8J,MAAM,EAAE,SAAS;YACjBhI,UAAU,EAAE,+BAA+B;YAC3C3B,UAAU,EAAE;UACd,CAAE;UAAAQ,QAAA,EAED6C,YAAY,GAAG,yBAAyB,GAAG;QAAwB;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGN,CAAC,eAENxG,OAAA;QAAK+E,SAAS,EAAC,mBAAmB;QAACD,KAAK,EAAE;UACxCqC,SAAS,EAAE,MAAM;UACjBlC,OAAO,EAAE,MAAM;UACfN,UAAU,EAAE,yBAAyB;UACrCO,YAAY,EAAE,MAAM;UACpBN,MAAM,EAAE,mCAAmC;UAC3CyC,SAAS,EAAE;QACb,CAAE;QAAAxB,QAAA,eACA7F,OAAA;UAAG8E,KAAK,EAAE;YAAEgC,KAAK,EAAE,SAAS;YAAE2H,SAAS,EAAE,QAAQ;YAAE7H,QAAQ,EAAE;UAAS,CAAE;UAAAf,QAAA,EAAC;QAEzE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC4B,EAAA,CA3eIF,UAAU;EAAA,QAcVtI,WAAW,EAKGC,YAAY,EAC9BC,oBAAoB;AAAA;AAAAwP,GAAA,GApBhBpH,UAAU;AA6ehB,eAAeA,UAAU;AAAC,IAAAV,EAAA,EAAAS,GAAA,EAAAqH,GAAA;AAAAC,YAAA,CAAA/H,EAAA;AAAA+H,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}