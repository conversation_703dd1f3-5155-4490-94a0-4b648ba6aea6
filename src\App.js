import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import LandingPage from './components/LandingPage';
import ZodiacPage from './components/ZodiacPage';
import AnalyticsDebugger from './components/AnalyticsDebugger';
import LanguageSelector from './components/LanguageSelector';
import { LanguageProvider } from './contexts/LanguageContext';
import { useAnalytics } from './hooks/useAnalytics';
import { initGA, setUserProperties } from './services/analytics';
import './App.css';

// Cache busting utility
const cacheBuster = Date.now();
window.CACHE_VERSION = cacheBuster;

const zodiacSigns = [
  { id: 'aries', sinhala: 'මේෂ', english: 'Aries', tamil: 'மேஷம்' },
  { id: 'taurus', sinhala: 'වෘෂභ', english: 'Taurus', tamil: 'ரிஷபம்' },
  { id: 'gemini', sinhala: 'මිථුන', english: 'Gemini', tamil: 'மிதுனம்' },
  { id: 'cancer', sinhala: 'කටක', english: 'Cancer', tamil: 'கடகம்' },
  { id: 'leo', sinhala: 'සිංහ', english: 'Leo', tamil: 'சிம்மம்' },
  { id: 'virgo', sinhala: 'කන්‍යා', english: 'Virgo', tamil: 'கன்னி' },
  { id: 'libra', sinhala: 'තුලා', english: 'Libra', tamil: 'துலாம்' },
  { id: 'scorpio', sinhala: 'වෘශ්චික', english: 'Scorpio', tamil: 'விருச்சிகம்' },
  { id: 'sagittarius', sinhala: 'ධනු', english: 'Sagittarius', tamil: 'தனுசு' },
  { id: 'capricorn', sinhala: 'මකර', english: 'Capricorn', tamil: 'மகரம்' },
  { id: 'aquarius', sinhala: 'කුම්භ', english: 'Aquarius', tamil: 'கும்பம்' },
  { id: 'pisces', sinhala: 'මීන', english: 'Pisces', tamil: 'மீனம்' }
];

function App() {
  useEffect(() => {
    // Initialize Google Analytics
    const gaInitialized = initGA();

    if (gaInitialized) {
      // Set initial user properties
      setUserProperties({
        website_language: 'sinhala',
        website_type: 'astrology',
        content_category: 'horoscope'
      });
    }

    // Disable right-click context menu
    const handleContextMenu = (e) => {
      e.preventDefault();
      return false;
    };

    // Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S, and other developer shortcuts
    const handleKeyDown = (e) => {
      // F12
      if (e.keyCode === 123) {
        e.preventDefault();
        return false;
      }
      // Ctrl+Shift+I (Developer Tools)
      if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
        e.preventDefault();
        return false;
      }
      // Ctrl+U (View Source)
      if (e.ctrlKey && e.keyCode === 85) {
        e.preventDefault();
        return false;
      }
      // Ctrl+S (Save Page)
      if (e.ctrlKey && e.keyCode === 83) {
        e.preventDefault();
        return false;
      }
      // Ctrl+A (Select All)
      if (e.ctrlKey && e.keyCode === 65) {
        e.preventDefault();
        return false;
      }
      // Ctrl+C (Copy)
      if (e.ctrlKey && e.keyCode === 67) {
        e.preventDefault();
        return false;
      }
    };

    // Disable drag and drop
    const handleDragStart = (e) => {
      e.preventDefault();
      return false;
    };

    // Add event listeners
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('dragstart', handleDragStart);

    // Cleanup event listeners
    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('dragstart', handleDragStart);
    };
  }, []);

  return (
    <LanguageProvider>
      <Router future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}>
        <div className="App">
          {/* Language Selector */}
          <LanguageSelector position="top-right" />

          <Routes>
            <Route path="/" element={<LandingPage zodiacSigns={zodiacSigns} />} />
            {zodiacSigns.map(sign => (
              <Route
                key={sign.id}
                path={`/${sign.id}`}
                element={<ZodiacPage sign={sign} />}
              />
            ))}
          </Routes>

          {/* Analytics Debugger - only shows in development */}
          <AnalyticsDebugger />
        </div>
      </Router>
    </LanguageProvider>
  );
}

export default App;