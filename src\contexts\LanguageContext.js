import React, { createContext, useContext, useState, useEffect } from 'react';
import translationService from '../services/TranslationService';

// Create Language Context
const LanguageContext = createContext();

// Custom hook to use Language Context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Language Provider Component
export const LanguageProvider = ({ children }) => {
  // Initialize language from localStorage or default to Sinhala
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    try {
      const savedLanguage = localStorage.getItem('horoscope_language');
      return savedLanguage || 'si'; // Default to Sinhala
    } catch (error) {
      console.warn('Error reading language from localStorage:', error);
      return 'si';
    }
  });

  // Loading state for translations
  const [isTranslating, setIsTranslating] = useState(false);

  // Available languages
  const availableLanguages = translationService.getAvailableLanguages();

  // Save language preference to localStorage
  useEffect(() => {
    try {
      localStorage.setItem('horoscope_language', currentLanguage);
    } catch (error) {
      console.warn('Error saving language to localStorage:', error);
    }
  }, [currentLanguage]);

  // Change language function
  const changeLanguage = async (newLanguage) => {
    if (newLanguage === currentLanguage) {
      return; // No change needed
    }

    if (!availableLanguages[newLanguage]) {
      console.error('Invalid language code:', newLanguage);
      return;
    }

    setIsTranslating(true);
    
    try {
      // Clear any existing translation cache when language changes
      translationService.clearCache();
      
      // Update current language
      setCurrentLanguage(newLanguage);
      
      console.log(`Language changed to: ${availableLanguages[newLanguage].name}`);
    } catch (error) {
      console.error('Error changing language:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  // Get current language info
  const getCurrentLanguageInfo = () => {
    return translationService.getLanguageInfo(currentLanguage);
  };

  // Translate content function
  const translateContent = async (content, fromLanguage = 'si') => {
    if (!content || currentLanguage === fromLanguage) {
      return content;
    }

    try {
      setIsTranslating(true);
      const translatedContent = await translationService.translateContent(
        content,
        fromLanguage,
        currentLanguage
      );
      return translatedContent;
    } catch (error) {
      console.error('Translation error:', error);
      return content; // Return original content as fallback
    } finally {
      setIsTranslating(false);
    }
  };

  // Translate horoscope categories
  const translateHoroscopeCategories = async (categories, fromLanguage = 'si') => {
    if (!categories || currentLanguage === fromLanguage) {
      return categories;
    }

    try {
      setIsTranslating(true);
      const translatedCategories = await translationService.translateHoroscopeCategories(
        categories,
        fromLanguage,
        currentLanguage
      );
      return translatedCategories;
    } catch (error) {
      console.error('Horoscope translation error:', error);
      return categories; // Return original categories as fallback
    } finally {
      setIsTranslating(false);
    }
  };

  // Get UI text in current language
  const getUIText = (textKey) => {
    return translationService.translateUIText(textKey, currentLanguage);
  };

  // Get all UI texts for current language
  const getUITexts = () => {
    const uiTexts = {
      si: {
        todayHoroscope: 'අද දිනයේ රාශිඵල',
        horoscopeDate: 'රාශිඵල දිනය',
        lastUpdated: 'අවසන් වරට යාවත්කාලීන කළේ',
        loading: 'රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.',
        refreshing: 'නව රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.',
        retry: 'නැවත උත්සාහ කරන්න',
        backToHome: 'මුල් පිටුවට',
        selectLanguage: 'භාෂාව තෝරන්න',
        love: 'ආදරය',
        career: 'වෘත්තිය',
        health: 'සෞඛ්‍යය',
        finance: 'මූල්‍ය',
        general: 'සාමාන්‍ය',
        generatingHoroscope: 'රාශිඵල සකස් කරමින්... කරුණාකර රැඳී සිටින්න.',
        horoscopeTitle: 'රාශිඵල',
        zodiacSign: 'රාශිය'
      },
      en: {
        todayHoroscope: "Today's Horoscope",
        horoscopeDate: 'Horoscope Date',
        lastUpdated: 'Last Updated',
        loading: 'Loading horoscope... Please wait.',
        refreshing: 'Getting fresh horoscope... Please wait.',
        retry: 'Try Again',
        backToHome: 'Back to Home',
        selectLanguage: 'Select Language',
        love: 'Love',
        career: 'Career',
        health: 'Health',
        finance: 'Finance',
        general: 'General',
        generatingHoroscope: 'Generating horoscope... Please wait.',
        horoscopeTitle: 'Horoscope',
        zodiacSign: 'Zodiac Sign'
      },
      ta: {
        todayHoroscope: 'இன்றைய ராசிபலன்',
        horoscopeDate: 'ராசிபலன் தேதி',
        lastUpdated: 'கடைசியாக புதுப்பிக்கப்பட்டது',
        loading: 'ராசிபலன் ஏற்றுகிறது... தயவுசெய்து காத்திருக்கவும்.',
        refreshing: 'புதிய ராசிபலன் பெறுகிறது... தயவுசெய்து காத்திருக்கவும்.',
        retry: 'மீண்டும் முயற்சிக்கவும்',
        backToHome: 'முகப்புக்கு திரும்பு',
        selectLanguage: 'மொழியைத் தேர்ந்தெடுக்கவும்',
        love: 'காதல்',
        career: 'தொழில்',
        health: 'ஆரோக்கியம்',
        finance: 'நிதி',
        general: 'பொது',
        generatingHoroscope: 'ராசிபலன் உருவாக்குகிறது... தயவுசெய்து காத்திருக்கவும்.',
        horoscopeTitle: 'ராசிபலன்',
        zodiacSign: 'ராசி'
      }
    };

    return uiTexts[currentLanguage] || uiTexts['si'];
  };

  // Context value
  const contextValue = {
    currentLanguage,
    availableLanguages,
    isTranslating,
    changeLanguage,
    getCurrentLanguageInfo,
    translateContent,
    translateHoroscopeCategories,
    getUIText,
    getUITexts
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext;
