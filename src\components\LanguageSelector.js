import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const LanguageSelector = ({ position = 'top-right' }) => {
  const { 
    currentLanguage, 
    availableLanguages, 
    changeLanguage, 
    isTranslating,
    getUITexts 
  } = useLanguage();
  
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const uiTexts = getUITexts();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle language selection
  const handleLanguageSelect = async (languageCode) => {
    setIsOpen(false);
    await changeLanguage(languageCode);
  };

  // Get position styles
  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed',
      zIndex: 1000
    };

    switch (position) {
      case 'top-right':
        return { ...baseStyles, top: '20px', right: '20px' };
      case 'top-left':
        return { ...baseStyles, top: '20px', left: '20px' };
      case 'bottom-right':
        return { ...baseStyles, bottom: '20px', right: '20px' };
      case 'bottom-left':
        return { ...baseStyles, bottom: '20px', left: '20px' };
      default:
        return { ...baseStyles, top: '20px', right: '20px' };
    }
  };

  const currentLangInfo = availableLanguages[currentLanguage];

  return (
    <div 
      ref={dropdownRef}
      style={{
        ...getPositionStyles(),
        fontFamily: 'Noto Sans Sinhala, sans-serif'
      }}
    >
      {/* Language Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isTranslating}
        style={{
          background: 'rgba(244, 208, 63, 0.1)',
          border: '2px solid rgba(244, 208, 63, 0.3)',
          borderRadius: '25px',
          padding: '12px 20px',
          color: '#f4d03f',
          fontSize: '1rem',
          fontWeight: '600',
          cursor: isTranslating ? 'not-allowed' : 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
          transition: 'all 0.3s ease',
          minWidth: '140px',
          justifyContent: 'center',
          opacity: isTranslating ? 0.7 : 1,
          transform: isOpen ? 'scale(1.05)' : 'scale(1)',
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
        }}
        onMouseEnter={(e) => {
          if (!isTranslating) {
            e.target.style.background = 'rgba(244, 208, 63, 0.2)';
            e.target.style.borderColor = 'rgba(244, 208, 63, 0.5)';
            e.target.style.boxShadow = '0 12px 40px rgba(244, 208, 63, 0.2)';
          }
        }}
        onMouseLeave={(e) => {
          if (!isTranslating) {
            e.target.style.background = 'rgba(244, 208, 63, 0.1)';
            e.target.style.borderColor = 'rgba(244, 208, 63, 0.3)';
            e.target.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
          }
        }}
      >
        {isTranslating ? (
          <>
            <div 
              style={{
                width: '16px',
                height: '16px',
                border: '2px solid rgba(244, 208, 63, 0.3)',
                borderTop: '2px solid #f4d03f',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}
            />
            <span>...</span>
          </>
        ) : (
          <>
            <span style={{ fontSize: '1.2rem' }}>{currentLangInfo?.flag}</span>
            <span>{currentLangInfo?.name}</span>
            <span 
              style={{
                fontSize: '0.8rem',
                transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                transition: 'transform 0.3s ease'
              }}
            >
              ▼
            </span>
          </>
        )}
      </button>

      {/* Dropdown Menu */}
      {isOpen && !isTranslating && (
        <div
          style={{
            position: 'absolute',
            top: '100%',
            right: '0',
            marginTop: '10px',
            background: 'rgba(26, 26, 46, 0.95)',
            border: '2px solid rgba(244, 208, 63, 0.3)',
            borderRadius: '20px',
            backdropFilter: 'blur(15px)',
            boxShadow: '0 15px 50px rgba(0, 0, 0, 0.5)',
            overflow: 'hidden',
            minWidth: '180px',
            animation: 'fadeInDown 0.3s ease'
          }}
        >
          {/* Dropdown Header */}
          <div
            style={{
              padding: '15px 20px 10px',
              borderBottom: '1px solid rgba(244, 208, 63, 0.2)',
              color: '#f4d03f',
              fontSize: '0.9rem',
              fontWeight: '600',
              textAlign: 'center',
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
            }}
          >
            {uiTexts.selectLanguage}
          </div>

          {/* Language Options */}
          {Object.entries(availableLanguages).map(([code, langInfo]) => (
            <button
              key={code}
              onClick={() => handleLanguageSelect(code)}
              style={{
                width: '100%',
                padding: '15px 20px',
                background: currentLanguage === code 
                  ? 'rgba(244, 208, 63, 0.2)' 
                  : 'transparent',
                border: 'none',
                color: currentLanguage === code ? '#f4d03f' : '#e8f4fd',
                fontSize: '1rem',
                fontWeight: currentLanguage === code ? '600' : '400',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                transition: 'all 0.3s ease',
                textAlign: 'left',
                fontFamily: 'inherit'
              }}
              onMouseEnter={(e) => {
                if (currentLanguage !== code) {
                  e.target.style.background = 'rgba(244, 208, 63, 0.1)';
                  e.target.style.color = '#f4d03f';
                }
              }}
              onMouseLeave={(e) => {
                if (currentLanguage !== code) {
                  e.target.style.background = 'transparent';
                  e.target.style.color = '#e8f4fd';
                }
              }}
            >
              <span style={{ fontSize: '1.3rem' }}>{langInfo.flag}</span>
              <span>{langInfo.name}</span>
              {currentLanguage === code && (
                <span 
                  style={{ 
                    marginLeft: 'auto', 
                    fontSize: '1.2rem',
                    color: '#f4d03f'
                  }}
                >
                  ✓
                </span>
              )}
            </button>
          ))}
        </div>
      )}

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @keyframes fadeInDown {
          0% {
            opacity: 0;
            transform: translateY(-10px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
};

export default LanguageSelector;
