import axios from 'axios';

class TranslationService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    this.cache = new Map();
    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    
    // Language configurations
    this.languages = {
      si: { name: 'සිංහල', code: 'si', flag: '🇱🇰' },
      en: { name: 'English', code: 'en', flag: '🇺🇸' },
      ta: { name: 'தமிழ்', code: 'ta', flag: '🇱🇰' }
    };
    
    // Default language is Sinhala
    this.defaultLanguage = 'si';
  }

  // Get available languages
  getAvailableLanguages() {
    return this.languages;
  }

  // Get language info by code
  getLanguageInfo(code) {
    return this.languages[code] || this.languages[this.defaultLanguage];
  }

  // Generate cache key for translation
  getCacheKey(content, fromLang, toLang) {
    const contentHash = this.hashString(content);
    return `translation_${contentHash}_${fromLang}_${toLang}`;
  }

  // Simple hash function for content
  hashString(str) {
    let hash = 0;
    if (str.length === 0) return hash;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  // Get cached translation
  getCachedTranslation(content, fromLang, toLang) {
    const cacheKey = this.getCacheKey(content, fromLang, toLang);
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      } else {
        this.cache.delete(cacheKey);
      }
    }
    
    return null;
  }

  // Cache translation
  cacheTranslation(content, fromLang, toLang, translation) {
    const cacheKey = this.getCacheKey(content, fromLang, toLang);
    
    this.cache.set(cacheKey, {
      data: translation,
      timestamp: Date.now()
    });
  }

  // Translate content using backend API
  async translateContent(content, fromLanguage, toLanguage) {
    try {
      // If source and target languages are the same, return original content
      if (fromLanguage === toLanguage) {
        return content;
      }

      // Check cache first
      const cached = this.getCachedTranslation(content, fromLanguage, toLanguage);
      if (cached) {
        return cached;
      }

      // Call backend translation API
      const response = await axios.post(`${this.baseURL}/translate`, {
        content: content,
        fromLanguage: fromLanguage,
        toLanguage: toLanguage
      }, {
        timeout: 15000, // 15 second timeout
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        }
      });

      if (response.data && response.data.success) {
        const translatedContent = response.data.data.translatedContent;
        
        // Cache the translation
        this.cacheTranslation(content, fromLanguage, toLanguage, translatedContent);
        
        return translatedContent;
      } else {
        throw new Error('Translation API returned invalid response');
      }
    } catch (error) {
      console.error('Translation error:', error);
      
      // Return original content as fallback
      console.warn('Translation failed, returning original content');
      return content;
    }
  }

  // Translate horoscope categories
  async translateHoroscopeCategories(categories, fromLanguage, toLanguage) {
    try {
      if (fromLanguage === toLanguage) {
        return categories;
      }

      const translatedCategories = {};
      
      // Translate each category
      for (const [categoryKey, content] of Object.entries(categories)) {
        if (content && typeof content === 'string') {
          translatedCategories[categoryKey] = await this.translateContent(
            content, 
            fromLanguage, 
            toLanguage
          );
        } else {
          translatedCategories[categoryKey] = content;
        }
      }

      return translatedCategories;
    } catch (error) {
      console.error('Error translating horoscope categories:', error);
      return categories; // Return original as fallback
    }
  }

  // Translate UI text elements
  async translateUIText(textKey, toLanguage) {
    const uiTexts = {
      si: {
        todayHoroscope: 'අද දිනයේ රාශිඵල',
        horoscopeDate: 'රාශිඵල දිනය',
        lastUpdated: 'අවසන් වරට යාවත්කාලීන කළේ',
        loading: 'රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.',
        refreshing: 'නව රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.',
        retry: 'නැවත උත්සාහ කරන්න',
        backToHome: 'මුල් පිටුවට',
        selectLanguage: 'භාෂාව තෝරන්න',
        love: 'ආදරය',
        career: 'වෘත්තිය',
        health: 'සෞඛ්‍යය',
        finance: 'මූල්‍ය',
        general: 'සාමාන්‍ය'
      },
      en: {
        todayHoroscope: "Today's Horoscope",
        horoscopeDate: 'Horoscope Date',
        lastUpdated: 'Last Updated',
        loading: 'Loading horoscope... Please wait.',
        refreshing: 'Getting fresh horoscope... Please wait.',
        retry: 'Try Again',
        backToHome: 'Back to Home',
        selectLanguage: 'Select Language',
        love: 'Love',
        career: 'Career',
        health: 'Health',
        finance: 'Finance',
        general: 'General'
      },
      ta: {
        todayHoroscope: 'இன்றைய ராசிபலன்',
        horoscopeDate: 'ராசிபலன் தேதி',
        lastUpdated: 'கடைசியாக புதுப்பிக்கப்பட்டது',
        loading: 'ராசிபலன் ஏற்றுகிறது... தயவுசெய்து காத்திருக்கவும்.',
        refreshing: 'புதிய ராசிபலன் பெறுகிறது... தயவுசெய்து காத்திருக்கவும்.',
        retry: 'மீண்டும் முயற்சிக்கவும்',
        backToHome: 'முகப்புக்கு திரும்பு',
        selectLanguage: 'மொழியைத் தேர்ந்தெடுக்கவும்',
        love: 'காதல்',
        career: 'தொழில்',
        health: 'ஆரோக்கியம்',
        finance: 'நிதி',
        general: 'பொது'
      }
    };

    return uiTexts[toLanguage]?.[textKey] || uiTexts[this.defaultLanguage]?.[textKey] || textKey;
  }

  // Get all UI texts for a language
  getUITexts(language) {
    return this.translateUIText('', language);
  }

  // Clear translation cache
  clearCache() {
    this.cache.clear();
  }

  // Get cache statistics
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

const translationService = new TranslationService();
export default translationService;
