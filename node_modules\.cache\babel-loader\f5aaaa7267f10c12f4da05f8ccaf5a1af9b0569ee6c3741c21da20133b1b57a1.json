{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LandingPage from './components/LandingPage';\nimport ZodiacPage from './components/ZodiacPage';\nimport AnalyticsDebugger from './components/AnalyticsDebugger';\nimport LanguageSelector from './components/LanguageSelector';\nimport { LanguageProvider } from './contexts/LanguageContext';\nimport { useAnalytics } from './hooks/useAnalytics';\nimport { initGA, setUserProperties } from './services/analytics';\nimport './App.css';\n\n// Cache busting utility\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst cacheBuster = Date.now();\nwindow.CACHE_VERSION = cacheBuster;\nconst zodiacSigns = [{\n  id: 'aries',\n  sinhala: 'මේෂ',\n  english: 'Aries',\n  tamil: 'மேஷம்'\n}, {\n  id: 'taurus',\n  sinhala: 'වෘෂභ',\n  english: 'Taurus',\n  tamil: 'ரிஷபம்'\n}, {\n  id: 'gemini',\n  sinhala: 'මිථුන',\n  english: 'Gemini',\n  tamil: 'மிதுனம்'\n}, {\n  id: 'cancer',\n  sinhala: 'කටක',\n  english: 'Cancer',\n  tamil: 'கடகம்'\n}, {\n  id: 'leo',\n  sinhala: 'සිංහ',\n  english: 'Leo',\n  tamil: 'சிம்மம்'\n}, {\n  id: 'virgo',\n  sinhala: 'කන්‍යා',\n  english: 'Virgo',\n  tamil: 'கன்னி'\n}, {\n  id: 'libra',\n  sinhala: 'තුලා',\n  english: 'Libra',\n  tamil: 'துலாம்'\n}, {\n  id: 'scorpio',\n  sinhala: 'වෘශ්චික',\n  english: 'Scorpio',\n  tamil: 'விருச்சிகம்'\n}, {\n  id: 'sagittarius',\n  sinhala: 'ධනු',\n  english: 'Sagittarius',\n  tamil: 'தனுசு'\n}, {\n  id: 'capricorn',\n  sinhala: 'මකර',\n  english: 'Capricorn',\n  tamil: 'மகரம்'\n}, {\n  id: 'aquarius',\n  sinhala: 'කුම්භ',\n  english: 'Aquarius',\n  tamil: 'கும்பம்'\n}, {\n  id: 'pisces',\n  sinhala: 'මීන',\n  english: 'Pisces',\n  tamil: 'மீனம்'\n}];\nfunction App() {\n  _s();\n  useEffect(() => {\n    // Initialize Google Analytics\n    const gaInitialized = initGA();\n    if (gaInitialized) {\n      // Set initial user properties\n      setUserProperties({\n        website_language: 'sinhala',\n        website_type: 'astrology',\n        content_category: 'horoscope'\n      });\n    }\n\n    // Disable right-click context menu\n    const handleContextMenu = e => {\n      e.preventDefault();\n      return false;\n    };\n\n    // Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S, and other developer shortcuts\n    const handleKeyDown = e => {\n      // F12\n      if (e.keyCode === 123) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+Shift+I (Developer Tools)\n      if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+U (View Source)\n      if (e.ctrlKey && e.keyCode === 85) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+S (Save Page)\n      if (e.ctrlKey && e.keyCode === 83) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+A (Select All)\n      if (e.ctrlKey && e.keyCode === 65) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+C (Copy)\n      if (e.ctrlKey && e.keyCode === 67) {\n        e.preventDefault();\n        return false;\n      }\n    };\n\n    // Disable drag and drop\n    const handleDragStart = e => {\n      e.preventDefault();\n      return false;\n    };\n\n    // Add event listeners\n    document.addEventListener('contextmenu', handleContextMenu);\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('dragstart', handleDragStart);\n\n    // Cleanup event listeners\n    return () => {\n      document.removeEventListener('contextmenu', handleContextMenu);\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('dragstart', handleDragStart);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(LanguageProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      future: {\n        v7_startTransition: true,\n        v7_relativeSplatPath: true\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: [/*#__PURE__*/_jsxDEV(LanguageSelector, {\n          position: \"top-right\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(LandingPage, {\n              zodiacSigns: zodiacSigns\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), zodiacSigns.map(sign => /*#__PURE__*/_jsxDEV(Route, {\n            path: `/${sign.id}`,\n            element: /*#__PURE__*/_jsxDEV(ZodiacPage, {\n              sign: sign\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 26\n            }, this)\n          }, sign.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnalyticsDebugger, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "LandingPage", "ZodiacPage", "AnalyticsDebugger", "LanguageSelector", "LanguageProvider", "useAnalytics", "initGA", "setUserProperties", "jsxDEV", "_jsxDEV", "cacheBuster", "Date", "now", "window", "CACHE_VERSION", "zodiacSigns", "id", "sinhala", "english", "tamil", "App", "_s", "gaInitialized", "website_language", "website_type", "content_category", "handleContextMenu", "e", "preventDefault", "handleKeyDown", "keyCode", "ctrl<PERSON>ey", "shift<PERSON>ey", "handleDragStart", "document", "addEventListener", "removeEventListener", "children", "future", "v7_startTransition", "v7_relativeSplatPath", "className", "position", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "map", "sign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/App.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LandingPage from './components/LandingPage';\nimport ZodiacPage from './components/ZodiacPage';\nimport AnalyticsDebugger from './components/AnalyticsDebugger';\nimport LanguageSelector from './components/LanguageSelector';\nimport { LanguageProvider } from './contexts/LanguageContext';\nimport { useAnalytics } from './hooks/useAnalytics';\nimport { initGA, setUserProperties } from './services/analytics';\nimport './App.css';\n\n// Cache busting utility\nconst cacheBuster = Date.now();\nwindow.CACHE_VERSION = cacheBuster;\n\nconst zodiacSigns = [\n  { id: 'aries', sinhala: 'මේෂ', english: 'Aries', tamil: 'மேஷம்' },\n  { id: 'taurus', sinhala: 'වෘෂභ', english: 'Taurus', tamil: 'ரிஷபம்' },\n  { id: 'gemini', sinhala: 'මිථුන', english: 'Gemini', tamil: 'மிதுனம்' },\n  { id: 'cancer', sinhala: 'කටක', english: 'Cancer', tamil: 'கடகம்' },\n  { id: 'leo', sinhala: 'සිංහ', english: 'Leo', tamil: 'சிம்மம்' },\n  { id: 'virgo', sinhala: 'කන්‍යා', english: 'Virgo', tamil: 'கன்னி' },\n  { id: 'libra', sinhala: 'තුලා', english: 'Libra', tamil: 'துலாம்' },\n  { id: 'scorpio', sinhala: 'වෘශ්චික', english: 'Scorpio', tamil: 'விருச்சிகம்' },\n  { id: 'sagittarius', sinhala: 'ධනු', english: 'Sagittarius', tamil: 'தனுசு' },\n  { id: 'capricorn', sinhala: 'මකර', english: 'Capricorn', tamil: 'மகரம்' },\n  { id: 'aquarius', sinhala: 'කුම්භ', english: 'Aquarius', tamil: 'கும்பம்' },\n  { id: 'pisces', sinhala: 'මීන', english: 'Pisces', tamil: 'மீனம்' }\n];\n\nfunction App() {\n  useEffect(() => {\n    // Initialize Google Analytics\n    const gaInitialized = initGA();\n\n    if (gaInitialized) {\n      // Set initial user properties\n      setUserProperties({\n        website_language: 'sinhala',\n        website_type: 'astrology',\n        content_category: 'horoscope'\n      });\n    }\n\n    // Disable right-click context menu\n    const handleContextMenu = (e) => {\n      e.preventDefault();\n      return false;\n    };\n\n    // Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S, and other developer shortcuts\n    const handleKeyDown = (e) => {\n      // F12\n      if (e.keyCode === 123) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+Shift+I (Developer Tools)\n      if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+U (View Source)\n      if (e.ctrlKey && e.keyCode === 85) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+S (Save Page)\n      if (e.ctrlKey && e.keyCode === 83) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+A (Select All)\n      if (e.ctrlKey && e.keyCode === 65) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+C (Copy)\n      if (e.ctrlKey && e.keyCode === 67) {\n        e.preventDefault();\n        return false;\n      }\n    };\n\n    // Disable drag and drop\n    const handleDragStart = (e) => {\n      e.preventDefault();\n      return false;\n    };\n\n    // Add event listeners\n    document.addEventListener('contextmenu', handleContextMenu);\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('dragstart', handleDragStart);\n\n    // Cleanup event listeners\n    return () => {\n      document.removeEventListener('contextmenu', handleContextMenu);\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('dragstart', handleDragStart);\n    };\n  }, []);\n\n  return (\n    <LanguageProvider>\n      <Router future={{\n        v7_startTransition: true,\n        v7_relativeSplatPath: true\n      }}>\n        <div className=\"App\">\n          {/* Language Selector */}\n          <LanguageSelector position=\"top-right\" />\n\n          <Routes>\n            <Route path=\"/\" element={<LandingPage zodiacSigns={zodiacSigns} />} />\n            {zodiacSigns.map(sign => (\n              <Route\n                key={sign.id}\n                path={`/${sign.id}`}\n                element={<ZodiacPage sign={sign} />}\n              />\n            ))}\n          </Routes>\n\n          {/* Analytics Debugger - only shows in development */}\n          <AnalyticsDebugger />\n        </div>\n      </Router>\n    </LanguageProvider>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,MAAM,EAAEC,iBAAiB,QAAQ,sBAAsB;AAChE,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;AAC9BC,MAAM,CAACC,aAAa,GAAGJ,WAAW;AAElC,MAAMK,WAAW,GAAG,CAClB;EAAEC,EAAE,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,EACjE;EAAEH,EAAE,EAAE,QAAQ;EAAEC,OAAO,EAAE,MAAM;EAAEC,OAAO,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACrE;EAAEH,EAAE,EAAE,QAAQ;EAAEC,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAU,CAAC,EACvE;EAAEH,EAAE,EAAE,QAAQ;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAQ,CAAC,EACnE;EAAEH,EAAE,EAAE,KAAK;EAAEC,OAAO,EAAE,MAAM;EAAEC,OAAO,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAU,CAAC,EAChE;EAAEH,EAAE,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,OAAO,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,EACpE;EAAEH,EAAE,EAAE,OAAO;EAAEC,OAAO,EAAE,MAAM;EAAEC,OAAO,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAS,CAAC,EACnE;EAAEH,EAAE,EAAE,SAAS;EAAEC,OAAO,EAAE,SAAS;EAAEC,OAAO,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAc,CAAC,EAC/E;EAAEH,EAAE,EAAE,aAAa;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAC7E;EAAEH,EAAE,EAAE,WAAW;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAQ,CAAC,EACzE;EAAEH,EAAE,EAAE,UAAU;EAAEC,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAU,CAAC,EAC3E;EAAEH,EAAE,EAAE,QAAQ;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAQ,CAAC,CACpE;AAED,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb1B,SAAS,CAAC,MAAM;IACd;IACA,MAAM2B,aAAa,GAAGhB,MAAM,CAAC,CAAC;IAE9B,IAAIgB,aAAa,EAAE;MACjB;MACAf,iBAAiB,CAAC;QAChBgB,gBAAgB,EAAE,SAAS;QAC3BC,YAAY,EAAE,WAAW;QACzBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;MAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,OAAO,KAAK;IACd,CAAC;;IAED;IACA,MAAMC,aAAa,GAAIF,CAAC,IAAK;MAC3B;MACA,IAAIA,CAAC,CAACG,OAAO,KAAK,GAAG,EAAE;QACrBH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;MACA;MACA,IAAID,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACK,QAAQ,IAAIL,CAAC,CAACG,OAAO,KAAK,EAAE,EAAE;QAC/CH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;MACA;MACA,IAAID,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACG,OAAO,KAAK,EAAE,EAAE;QACjCH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;MACA;MACA,IAAID,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACG,OAAO,KAAK,EAAE,EAAE;QACjCH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;MACA;MACA,IAAID,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACG,OAAO,KAAK,EAAE,EAAE;QACjCH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;MACA;MACA,IAAID,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACG,OAAO,KAAK,EAAE,EAAE;QACjCH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;IACF,CAAC;;IAED;IACA,MAAMK,eAAe,GAAIN,CAAC,IAAK;MAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,OAAO,KAAK;IACd,CAAC;;IAED;IACAM,QAAQ,CAACC,gBAAgB,CAAC,aAAa,EAAET,iBAAiB,CAAC;IAC3DQ,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEN,aAAa,CAAC;IACnDK,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEF,eAAe,CAAC;;IAEvD;IACA,OAAO,MAAM;MACXC,QAAQ,CAACE,mBAAmB,CAAC,aAAa,EAAEV,iBAAiB,CAAC;MAC9DQ,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEP,aAAa,CAAC;MACtDK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEH,eAAe,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACExB,OAAA,CAACL,gBAAgB;IAAAiC,QAAA,eACf5B,OAAA,CAACZ,MAAM;MAACyC,MAAM,EAAE;QACdC,kBAAkB,EAAE,IAAI;QACxBC,oBAAoB,EAAE;MACxB,CAAE;MAAAH,QAAA,eACA5B,OAAA;QAAKgC,SAAS,EAAC,KAAK;QAAAJ,QAAA,gBAElB5B,OAAA,CAACN,gBAAgB;UAACuC,QAAQ,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEzCrC,OAAA,CAACX,MAAM;UAAAuC,QAAA,gBACL5B,OAAA,CAACV,KAAK;YAACgD,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEvC,OAAA,CAACT,WAAW;cAACe,WAAW,EAAEA;YAAY;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACrE/B,WAAW,CAACkC,GAAG,CAACC,IAAI,iBACnBzC,OAAA,CAACV,KAAK;YAEJgD,IAAI,EAAE,IAAIG,IAAI,CAAClC,EAAE,EAAG;YACpBgC,OAAO,eAAEvC,OAAA,CAACR,UAAU;cAACiD,IAAI,EAAEA;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE,GAF/BI,IAAI,CAAClC,EAAE;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGb,CACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGTrC,OAAA,CAACP,iBAAiB;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEvB;AAACzB,EAAA,CApGQD,GAAG;AAAA+B,EAAA,GAAH/B,GAAG;AAsGZ,eAAeA,GAAG;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}