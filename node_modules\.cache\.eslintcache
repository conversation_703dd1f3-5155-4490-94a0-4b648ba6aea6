[{"C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useAnalytics.js": "3", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\analytics.js": "4", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LandingPage.js": "5", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js": "6", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\AnalyticsDebugger.js": "7", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ParticleBackground.js": "8", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\SmokeAnimation.js": "9", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaAnimation.js": "10", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCardSection.js": "11", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js": "12", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\config\\analytics.js": "13", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCheckout.js": "14", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LanguageSelector.js": "15", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\contexts\\LanguageContext.js": "16", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\TranslationService.js": "17"}, {"size": 253, "mtime": 1750790409543, "results": "18", "hashOfConfig": "19"}, {"size": 4524, "mtime": 1751807584062, "results": "20", "hashOfConfig": "19"}, {"size": 8069, "mtime": 1751564462690, "results": "21", "hashOfConfig": "19"}, {"size": 8332, "mtime": 1751565228966, "results": "22", "hashOfConfig": "19"}, {"size": 17296, "mtime": 1751807646180, "results": "23", "hashOfConfig": "19"}, {"size": 30807, "mtime": 1751807602861, "results": "24", "hashOfConfig": "19"}, {"size": 8284, "mtime": 1751565326750, "results": "25", "hashOfConfig": "19"}, {"size": 3837, "mtime": 1750614946000, "results": "26", "hashOfConfig": "19"}, {"size": 2802, "mtime": 1750614946000, "results": "27", "hashOfConfig": "19"}, {"size": 7496, "mtime": 1750614946000, "results": "28", "hashOfConfig": "19"}, {"size": 7302, "mtime": 1751564707800, "results": "29", "hashOfConfig": "19"}, {"size": 6127, "mtime": 1751389760889, "results": "30", "hashOfConfig": "19"}, {"size": 6410, "mtime": 1751565137421, "results": "31", "hashOfConfig": "19"}, {"size": 13813, "mtime": 1751565044144, "results": "32", "hashOfConfig": "19"}, {"size": 7617, "mtime": 1751807249417, "results": "33", "hashOfConfig": "19"}, {"size": 7330, "mtime": 1751807206733, "results": "34", "hashOfConfig": "19"}, {"size": 7380, "mtime": 1751807170624, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cwcvdy", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\App.js", ["87"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useAnalytics.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\analytics.js", ["88"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LandingPage.js", ["89", "90"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js", ["91"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\AnalyticsDebugger.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ParticleBackground.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\SmokeAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCardSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\config\\analytics.js", ["92"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCheckout.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LanguageSelector.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\contexts\\LanguageContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\TranslationService.js", [], [], {"ruleId": "93", "severity": 1, "message": "94", "line": 8, "column": 10, "nodeType": "95", "messageId": "96", "endLine": 8, "endColumn": 22}, {"ruleId": "93", "severity": 1, "message": "97", "line": 9, "column": 3, "nodeType": "95", "messageId": "96", "endLine": 9, "endColumn": 16}, {"ruleId": "93", "severity": 1, "message": "98", "line": 10, "column": 11, "nodeType": "95", "messageId": "96", "endLine": 10, "endColumn": 26}, {"ruleId": "93", "severity": 1, "message": "99", "line": 13, "column": 9, "nodeType": "95", "messageId": "96", "endLine": 13, "endColumn": 16}, {"ruleId": "100", "severity": 1, "message": "101", "line": 529, "column": 6, "nodeType": "102", "endLine": 529, "endColumn": 15, "suggestions": "103"}, {"ruleId": "104", "severity": 1, "message": "105", "line": 230, "column": 1, "nodeType": "106", "endLine": 248, "endColumn": 3}, "no-unused-vars", "'useAnalytics' is defined but never used.", "Identifier", "unusedVar", "'CUSTOM_EVENTS' is defined but never used.", "'currentLanguage' is assigned a value but never used.", "'uiTexts' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'analytics' and 'uiTexts.loading'. Either include them or remove the dependency array.", "ArrayExpression", ["107"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "108", "fix": "109"}, "Update the dependencies array to be: [analytics, sign.id, uiTexts.loading]", {"range": "110", "text": "111"}, [16914, 16923], "[analytics, sign.id, uiTexts.loading]"]