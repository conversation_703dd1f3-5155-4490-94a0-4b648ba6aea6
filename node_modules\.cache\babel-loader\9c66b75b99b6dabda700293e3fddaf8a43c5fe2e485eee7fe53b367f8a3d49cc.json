{"ast": null, "code": "import axios from 'axios';\nclass TranslationService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n\n    // Language configurations\n    this.languages = {\n      si: {\n        name: 'සිංහල',\n        code: 'si',\n        flag: '🇱🇰'\n      },\n      en: {\n        name: 'English',\n        code: 'en',\n        flag: '🇺🇸'\n      },\n      ta: {\n        name: 'தமிழ்',\n        code: 'ta',\n        flag: '🇱🇰'\n      }\n    };\n\n    // Default language is Sinhala\n    this.defaultLanguage = 'si';\n  }\n\n  // Get available languages\n  getAvailableLanguages() {\n    return this.languages;\n  }\n\n  // Get language info by code\n  getLanguageInfo(code) {\n    return this.languages[code] || this.languages[this.defaultLanguage];\n  }\n\n  // Generate cache key for translation\n  getCacheKey(content, fromLang, toLang) {\n    const contentHash = this.hashString(content);\n    return `translation_${contentHash}_${fromLang}_${toLang}`;\n  }\n\n  // Simple hash function for content\n  hashString(str) {\n    let hash = 0;\n    if (str.length === 0) return hash;\n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i);\n      hash = (hash << 5) - hash + char;\n      hash = hash & hash; // Convert to 32-bit integer\n    }\n    return Math.abs(hash).toString(36);\n  }\n\n  // Get cached translation\n  getCachedTranslation(content, fromLang, toLang) {\n    const cacheKey = this.getCacheKey(content, fromLang, toLang);\n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    return null;\n  }\n\n  // Cache translation\n  cacheTranslation(content, fromLang, toLang, translation) {\n    const cacheKey = this.getCacheKey(content, fromLang, toLang);\n    this.cache.set(cacheKey, {\n      data: translation,\n      timestamp: Date.now()\n    });\n  }\n\n  // Translate content using backend API\n  async translateContent(content, fromLanguage, toLanguage) {\n    try {\n      // If source and target languages are the same, return original content\n      if (fromLanguage === toLanguage) {\n        return content;\n      }\n\n      // Check cache first\n      const cached = this.getCachedTranslation(content, fromLanguage, toLanguage);\n      if (cached) {\n        return cached;\n      }\n\n      // Call backend translation API\n      const response = await axios.post(`${this.baseURL}/translate`, {\n        content: content,\n        fromLanguage: fromLanguage,\n        toLanguage: toLanguage\n      }, {\n        timeout: 15000,\n        // 15 second timeout\n        headers: {\n          'Content-Type': 'application/json',\n          'Cache-Control': 'no-cache'\n        }\n      });\n      if (response.data && response.data.success) {\n        const translatedContent = response.data.data.translatedContent;\n\n        // Cache the translation\n        this.cacheTranslation(content, fromLanguage, toLanguage, translatedContent);\n        return translatedContent;\n      } else {\n        throw new Error('Translation API returned invalid response');\n      }\n    } catch (error) {\n      console.error('Translation error:', error);\n\n      // Return original content as fallback\n      console.warn('Translation failed, returning original content');\n      return content;\n    }\n  }\n\n  // Translate horoscope categories\n  async translateHoroscopeCategories(categories, fromLanguage, toLanguage) {\n    try {\n      if (fromLanguage === toLanguage) {\n        return categories;\n      }\n      const translatedCategories = {};\n\n      // Translate each category\n      for (const [categoryKey, content] of Object.entries(categories)) {\n        if (content && typeof content === 'string') {\n          translatedCategories[categoryKey] = await this.translateContent(content, fromLanguage, toLanguage);\n        } else {\n          translatedCategories[categoryKey] = content;\n        }\n      }\n      return translatedCategories;\n    } catch (error) {\n      console.error('Error translating horoscope categories:', error);\n      return categories; // Return original as fallback\n    }\n  }\n\n  // Translate UI text elements\n  async translateUIText(textKey, toLanguage) {\n    var _uiTexts$toLanguage, _uiTexts$this$default;\n    const uiTexts = {\n      si: {\n        todayHoroscope: 'අද දිනයේ රාශිඵල',\n        horoscopeDate: 'රාශිඵල දිනය',\n        lastUpdated: 'අවසන් වරට යාවත්කාලීන කළේ',\n        loading: 'රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.',\n        refreshing: 'නව රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.',\n        retry: 'නැවත උත්සාහ කරන්න',\n        backToHome: 'මුල් පිටුවට',\n        selectLanguage: 'භාෂාව තෝරන්න',\n        love: 'ආදරය',\n        career: 'වෘත්තිය',\n        health: 'සෞඛ්‍යය',\n        finance: 'මූල්‍ය',\n        general: 'සාමාන්‍ය'\n      },\n      en: {\n        todayHoroscope: \"Today's Horoscope\",\n        horoscopeDate: 'Horoscope Date',\n        lastUpdated: 'Last Updated',\n        loading: 'Loading horoscope... Please wait.',\n        refreshing: 'Getting fresh horoscope... Please wait.',\n        retry: 'Try Again',\n        backToHome: 'Back to Home',\n        selectLanguage: 'Select Language',\n        love: 'Love',\n        career: 'Career',\n        health: 'Health',\n        finance: 'Finance',\n        general: 'General'\n      },\n      ta: {\n        todayHoroscope: 'இன்றைய ராசிபலன்',\n        horoscopeDate: 'ராசிபலன் தேதி',\n        lastUpdated: 'கடைசியாக புதுப்பிக்கப்பட்டது',\n        loading: 'ராசிபலன் ஏற்றுகிறது... தயவுசெய்து காத்திருக்கவும்.',\n        refreshing: 'புதிய ராசிபலன் பெறுகிறது... தயவுசெய்து காத்திருக்கவும்.',\n        retry: 'மீண்டும் முயற்சிக்கவும்',\n        backToHome: 'முகப்புக்கு திரும்பு',\n        selectLanguage: 'மொழியைத் தேர்ந்தெடுக்கவும்',\n        love: 'காதல்',\n        career: 'தொழில்',\n        health: 'ஆரோக்கியம்',\n        finance: 'நிதி',\n        general: 'பொது'\n      }\n    };\n    return ((_uiTexts$toLanguage = uiTexts[toLanguage]) === null || _uiTexts$toLanguage === void 0 ? void 0 : _uiTexts$toLanguage[textKey]) || ((_uiTexts$this$default = uiTexts[this.defaultLanguage]) === null || _uiTexts$this$default === void 0 ? void 0 : _uiTexts$this$default[textKey]) || textKey;\n  }\n\n  // Get all UI texts for a language\n  getUITexts(language) {\n    return this.translateUIText('', language);\n  }\n\n  // Clear translation cache\n  clearCache() {\n    this.cache.clear();\n  }\n\n  // Get cache statistics\n  getCacheStats() {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys())\n    };\n  }\n}\nconst translationService = new TranslationService();\nexport default translationService;", "map": {"version": 3, "names": ["axios", "TranslationService", "constructor", "baseURL", "process", "env", "REACT_APP_API_URL", "cache", "Map", "cacheExpiry", "languages", "si", "name", "code", "flag", "en", "ta", "defaultLanguage", "getAvailableLanguages", "getLanguageInfo", "get<PERSON><PERSON><PERSON><PERSON>", "content", "fromLang", "toLang", "contentHash", "hashString", "str", "hash", "length", "i", "char", "charCodeAt", "Math", "abs", "toString", "getCachedTranslation", "cache<PERSON>ey", "has", "cached", "get", "Date", "now", "timestamp", "data", "delete", "cacheTranslation", "translation", "set", "translate<PERSON>ontent", "fromLanguage", "toLanguage", "response", "post", "timeout", "headers", "success", "<PERSON><PERSON><PERSON><PERSON>", "Error", "error", "console", "warn", "translateHoroscopeCategories", "categories", "translatedCategories", "categoryKey", "Object", "entries", "translateUIText", "<PERSON><PERSON><PERSON>", "_uiTexts$toLanguage", "_uiTexts$this$default", "uiTexts", "todayHoroscope", "horoscopeDate", "lastUpdated", "loading", "refreshing", "retry", "backToHome", "selectLanguage", "love", "career", "health", "finance", "general", "getUITexts", "language", "clearCache", "clear", "getCacheStats", "size", "keys", "Array", "from", "translationService"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/services/TranslationService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass TranslationService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n    \n    // Language configurations\n    this.languages = {\n      si: { name: 'සිංහල', code: 'si', flag: '🇱🇰' },\n      en: { name: 'English', code: 'en', flag: '🇺🇸' },\n      ta: { name: 'தமிழ்', code: 'ta', flag: '🇱🇰' }\n    };\n    \n    // Default language is Sinhala\n    this.defaultLanguage = 'si';\n  }\n\n  // Get available languages\n  getAvailableLanguages() {\n    return this.languages;\n  }\n\n  // Get language info by code\n  getLanguageInfo(code) {\n    return this.languages[code] || this.languages[this.defaultLanguage];\n  }\n\n  // Generate cache key for translation\n  getCacheKey(content, fromLang, toLang) {\n    const contentHash = this.hashString(content);\n    return `translation_${contentHash}_${fromLang}_${toLang}`;\n  }\n\n  // Simple hash function for content\n  hashString(str) {\n    let hash = 0;\n    if (str.length === 0) return hash;\n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i);\n      hash = ((hash << 5) - hash) + char;\n      hash = hash & hash; // Convert to 32-bit integer\n    }\n    return Math.abs(hash).toString(36);\n  }\n\n  // Get cached translation\n  getCachedTranslation(content, fromLang, toLang) {\n    const cacheKey = this.getCacheKey(content, fromLang, toLang);\n    \n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    \n    return null;\n  }\n\n  // Cache translation\n  cacheTranslation(content, fromLang, toLang, translation) {\n    const cacheKey = this.getCacheKey(content, fromLang, toLang);\n    \n    this.cache.set(cacheKey, {\n      data: translation,\n      timestamp: Date.now()\n    });\n  }\n\n  // Translate content using backend API\n  async translateContent(content, fromLanguage, toLanguage) {\n    try {\n      // If source and target languages are the same, return original content\n      if (fromLanguage === toLanguage) {\n        return content;\n      }\n\n      // Check cache first\n      const cached = this.getCachedTranslation(content, fromLanguage, toLanguage);\n      if (cached) {\n        return cached;\n      }\n\n      // Call backend translation API\n      const response = await axios.post(`${this.baseURL}/translate`, {\n        content: content,\n        fromLanguage: fromLanguage,\n        toLanguage: toLanguage\n      }, {\n        timeout: 15000, // 15 second timeout\n        headers: {\n          'Content-Type': 'application/json',\n          'Cache-Control': 'no-cache'\n        }\n      });\n\n      if (response.data && response.data.success) {\n        const translatedContent = response.data.data.translatedContent;\n        \n        // Cache the translation\n        this.cacheTranslation(content, fromLanguage, toLanguage, translatedContent);\n        \n        return translatedContent;\n      } else {\n        throw new Error('Translation API returned invalid response');\n      }\n    } catch (error) {\n      console.error('Translation error:', error);\n      \n      // Return original content as fallback\n      console.warn('Translation failed, returning original content');\n      return content;\n    }\n  }\n\n  // Translate horoscope categories\n  async translateHoroscopeCategories(categories, fromLanguage, toLanguage) {\n    try {\n      if (fromLanguage === toLanguage) {\n        return categories;\n      }\n\n      const translatedCategories = {};\n      \n      // Translate each category\n      for (const [categoryKey, content] of Object.entries(categories)) {\n        if (content && typeof content === 'string') {\n          translatedCategories[categoryKey] = await this.translateContent(\n            content, \n            fromLanguage, \n            toLanguage\n          );\n        } else {\n          translatedCategories[categoryKey] = content;\n        }\n      }\n\n      return translatedCategories;\n    } catch (error) {\n      console.error('Error translating horoscope categories:', error);\n      return categories; // Return original as fallback\n    }\n  }\n\n  // Translate UI text elements\n  async translateUIText(textKey, toLanguage) {\n    const uiTexts = {\n      si: {\n        todayHoroscope: 'අද දිනයේ රාශිඵල',\n        horoscopeDate: 'රාශිඵල දිනය',\n        lastUpdated: 'අවසන් වරට යාවත්කාලීන කළේ',\n        loading: 'රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.',\n        refreshing: 'නව රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.',\n        retry: 'නැවත උත්සාහ කරන්න',\n        backToHome: 'මුල් පිටුවට',\n        selectLanguage: 'භාෂාව තෝරන්න',\n        love: 'ආදරය',\n        career: 'වෘත්තිය',\n        health: 'සෞඛ්‍යය',\n        finance: 'මූල්‍ය',\n        general: 'සාමාන්‍ය'\n      },\n      en: {\n        todayHoroscope: \"Today's Horoscope\",\n        horoscopeDate: 'Horoscope Date',\n        lastUpdated: 'Last Updated',\n        loading: 'Loading horoscope... Please wait.',\n        refreshing: 'Getting fresh horoscope... Please wait.',\n        retry: 'Try Again',\n        backToHome: 'Back to Home',\n        selectLanguage: 'Select Language',\n        love: 'Love',\n        career: 'Career',\n        health: 'Health',\n        finance: 'Finance',\n        general: 'General'\n      },\n      ta: {\n        todayHoroscope: 'இன்றைய ராசிபலன்',\n        horoscopeDate: 'ராசிபலன் தேதி',\n        lastUpdated: 'கடைசியாக புதுப்பிக்கப்பட்டது',\n        loading: 'ராசிபலன் ஏற்றுகிறது... தயவுசெய்து காத்திருக்கவும்.',\n        refreshing: 'புதிய ராசிபலன் பெறுகிறது... தயவுசெய்து காத்திருக்கவும்.',\n        retry: 'மீண்டும் முயற்சிக்கவும்',\n        backToHome: 'முகப்புக்கு திரும்பு',\n        selectLanguage: 'மொழியைத் தேர்ந்தெடுக்கவும்',\n        love: 'காதல்',\n        career: 'தொழில்',\n        health: 'ஆரோக்கியம்',\n        finance: 'நிதி',\n        general: 'பொது'\n      }\n    };\n\n    return uiTexts[toLanguage]?.[textKey] || uiTexts[this.defaultLanguage]?.[textKey] || textKey;\n  }\n\n  // Get all UI texts for a language\n  getUITexts(language) {\n    return this.translateUIText('', language);\n  }\n\n  // Clear translation cache\n  clearCache() {\n    this.cache.clear();\n  }\n\n  // Get cache statistics\n  getCacheStats() {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys())\n    };\n  }\n}\n\nconst translationService = new TranslationService();\nexport default translationService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,kBAAkB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;IAC3E,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,WAAW,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;;IAExC;IACA,IAAI,CAACC,SAAS,GAAG;MACfC,EAAE,EAAE;QAAEC,IAAI,EAAE,OAAO;QAAEC,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAO,CAAC;MAC/CC,EAAE,EAAE;QAAEH,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAO,CAAC;MACjDE,EAAE,EAAE;QAAEJ,IAAI,EAAE,OAAO;QAAEC,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAO;IAChD,CAAC;;IAED;IACA,IAAI,CAACG,eAAe,GAAG,IAAI;EAC7B;;EAEA;EACAC,qBAAqBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACR,SAAS;EACvB;;EAEA;EACAS,eAAeA,CAACN,IAAI,EAAE;IACpB,OAAO,IAAI,CAACH,SAAS,CAACG,IAAI,CAAC,IAAI,IAAI,CAACH,SAAS,CAAC,IAAI,CAACO,eAAe,CAAC;EACrE;;EAEA;EACAG,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAE;IACrC,MAAMC,WAAW,GAAG,IAAI,CAACC,UAAU,CAACJ,OAAO,CAAC;IAC5C,OAAO,eAAeG,WAAW,IAAIF,QAAQ,IAAIC,MAAM,EAAE;EAC3D;;EAEA;EACAE,UAAUA,CAACC,GAAG,EAAE;IACd,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAID,GAAG,CAACE,MAAM,KAAK,CAAC,EAAE,OAAOD,IAAI;IACjC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAGJ,GAAG,CAACK,UAAU,CAACF,CAAC,CAAC;MAC9BF,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAIG,IAAI;MAClCH,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC,CAAC;IACtB;IACA,OAAOK,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC,CAACO,QAAQ,CAAC,EAAE,CAAC;EACpC;;EAEA;EACAC,oBAAoBA,CAACd,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAE;IAC9C,MAAMa,QAAQ,GAAG,IAAI,CAAChB,WAAW,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,CAAC;IAE5D,IAAI,IAAI,CAAChB,KAAK,CAAC8B,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC5B,MAAME,MAAM,GAAG,IAAI,CAAC/B,KAAK,CAACgC,GAAG,CAACH,QAAQ,CAAC;MACvC,IAAII,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,MAAM,CAACI,SAAS,GAAG,IAAI,CAACjC,WAAW,EAAE;QACpD,OAAO6B,MAAM,CAACK,IAAI;MACpB,CAAC,MAAM;QACL,IAAI,CAACpC,KAAK,CAACqC,MAAM,CAACR,QAAQ,CAAC;MAC7B;IACF;IAEA,OAAO,IAAI;EACb;;EAEA;EACAS,gBAAgBA,CAACxB,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEuB,WAAW,EAAE;IACvD,MAAMV,QAAQ,GAAG,IAAI,CAAChB,WAAW,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,CAAC;IAE5D,IAAI,CAAChB,KAAK,CAACwC,GAAG,CAACX,QAAQ,EAAE;MACvBO,IAAI,EAAEG,WAAW;MACjBJ,SAAS,EAAEF,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMO,gBAAgBA,CAAC3B,OAAO,EAAE4B,YAAY,EAAEC,UAAU,EAAE;IACxD,IAAI;MACF;MACA,IAAID,YAAY,KAAKC,UAAU,EAAE;QAC/B,OAAO7B,OAAO;MAChB;;MAEA;MACA,MAAMiB,MAAM,GAAG,IAAI,CAACH,oBAAoB,CAACd,OAAO,EAAE4B,YAAY,EAAEC,UAAU,CAAC;MAC3E,IAAIZ,MAAM,EAAE;QACV,OAAOA,MAAM;MACf;;MAEA;MACA,MAAMa,QAAQ,GAAG,MAAMnD,KAAK,CAACoD,IAAI,CAAC,GAAG,IAAI,CAACjD,OAAO,YAAY,EAAE;QAC7DkB,OAAO,EAAEA,OAAO;QAChB4B,YAAY,EAAEA,YAAY;QAC1BC,UAAU,EAAEA;MACd,CAAC,EAAE;QACDG,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE;QACnB;MACF,CAAC,CAAC;MAEF,IAAIH,QAAQ,CAACR,IAAI,IAAIQ,QAAQ,CAACR,IAAI,CAACY,OAAO,EAAE;QAC1C,MAAMC,iBAAiB,GAAGL,QAAQ,CAACR,IAAI,CAACA,IAAI,CAACa,iBAAiB;;QAE9D;QACA,IAAI,CAACX,gBAAgB,CAACxB,OAAO,EAAE4B,YAAY,EAAEC,UAAU,EAAEM,iBAAiB,CAAC;QAE3E,OAAOA,iBAAiB;MAC1B,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,2CAA2C,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MACAC,OAAO,CAACC,IAAI,CAAC,gDAAgD,CAAC;MAC9D,OAAOvC,OAAO;IAChB;EACF;;EAEA;EACA,MAAMwC,4BAA4BA,CAACC,UAAU,EAAEb,YAAY,EAAEC,UAAU,EAAE;IACvE,IAAI;MACF,IAAID,YAAY,KAAKC,UAAU,EAAE;QAC/B,OAAOY,UAAU;MACnB;MAEA,MAAMC,oBAAoB,GAAG,CAAC,CAAC;;MAE/B;MACA,KAAK,MAAM,CAACC,WAAW,EAAE3C,OAAO,CAAC,IAAI4C,MAAM,CAACC,OAAO,CAACJ,UAAU,CAAC,EAAE;QAC/D,IAAIzC,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;UAC1C0C,oBAAoB,CAACC,WAAW,CAAC,GAAG,MAAM,IAAI,CAAChB,gBAAgB,CAC7D3B,OAAO,EACP4B,YAAY,EACZC,UACF,CAAC;QACH,CAAC,MAAM;UACLa,oBAAoB,CAACC,WAAW,CAAC,GAAG3C,OAAO;QAC7C;MACF;MAEA,OAAO0C,oBAAoB;IAC7B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,OAAOI,UAAU,CAAC,CAAC;IACrB;EACF;;EAEA;EACA,MAAMK,eAAeA,CAACC,OAAO,EAAElB,UAAU,EAAE;IAAA,IAAAmB,mBAAA,EAAAC,qBAAA;IACzC,MAAMC,OAAO,GAAG;MACd5D,EAAE,EAAE;QACF6D,cAAc,EAAE,iBAAiB;QACjCC,aAAa,EAAE,aAAa;QAC5BC,WAAW,EAAE,0BAA0B;QACvCC,OAAO,EAAE,6CAA6C;QACtDC,UAAU,EAAE,gDAAgD;QAC5DC,KAAK,EAAE,mBAAmB;QAC1BC,UAAU,EAAE,aAAa;QACzBC,cAAc,EAAE,cAAc;QAC9BC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,SAAS;QACjBC,OAAO,EAAE,QAAQ;QACjBC,OAAO,EAAE;MACX,CAAC;MACDrE,EAAE,EAAE;QACFyD,cAAc,EAAE,mBAAmB;QACnCC,aAAa,EAAE,gBAAgB;QAC/BC,WAAW,EAAE,cAAc;QAC3BC,OAAO,EAAE,mCAAmC;QAC5CC,UAAU,EAAE,yCAAyC;QACrDC,KAAK,EAAE,WAAW;QAClBC,UAAU,EAAE,cAAc;QAC1BC,cAAc,EAAE,iBAAiB;QACjCC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,QAAQ;QAChBC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,SAAS;QAClBC,OAAO,EAAE;MACX,CAAC;MACDpE,EAAE,EAAE;QACFwD,cAAc,EAAE,iBAAiB;QACjCC,aAAa,EAAE,eAAe;QAC9BC,WAAW,EAAE,8BAA8B;QAC3CC,OAAO,EAAE,oDAAoD;QAC7DC,UAAU,EAAE,yDAAyD;QACrEC,KAAK,EAAE,yBAAyB;QAChCC,UAAU,EAAE,sBAAsB;QAClCC,cAAc,EAAE,4BAA4B;QAC5CC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,QAAQ;QAChBC,MAAM,EAAE,YAAY;QACpBC,OAAO,EAAE,MAAM;QACfC,OAAO,EAAE;MACX;IACF,CAAC;IAED,OAAO,EAAAf,mBAAA,GAAAE,OAAO,CAACrB,UAAU,CAAC,cAAAmB,mBAAA,uBAAnBA,mBAAA,CAAsBD,OAAO,CAAC,OAAAE,qBAAA,GAAIC,OAAO,CAAC,IAAI,CAACtD,eAAe,CAAC,cAAAqD,qBAAA,uBAA7BA,qBAAA,CAAgCF,OAAO,CAAC,KAAIA,OAAO;EAC9F;;EAEA;EACAiB,UAAUA,CAACC,QAAQ,EAAE;IACnB,OAAO,IAAI,CAACnB,eAAe,CAAC,EAAE,EAAEmB,QAAQ,CAAC;EAC3C;;EAEA;EACAC,UAAUA,CAAA,EAAG;IACX,IAAI,CAAChF,KAAK,CAACiF,KAAK,CAAC,CAAC;EACpB;;EAEA;EACAC,aAAaA,CAAA,EAAG;IACd,OAAO;MACLC,IAAI,EAAE,IAAI,CAACnF,KAAK,CAACmF,IAAI;MACrBC,IAAI,EAAEC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtF,KAAK,CAACoF,IAAI,CAAC,CAAC;IACpC,CAAC;EACH;AACF;AAEA,MAAMG,kBAAkB,GAAG,IAAI7F,kBAAkB,CAAC,CAAC;AACnD,eAAe6F,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}